using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GMCadiomMeeting.Data.Models;

/// <summary>
/// Represents the status of a meeting
/// </summary>
public enum MeetingStatus
{
    Scheduled = 0,
    InProgress = 1,
    Ended = 2,
    Cancelled = 3
}

/// <summary>
/// Represents the type of meeting
/// </summary>
public enum MeetingType
{
    OneOnOne = 0,
    Group = 1,
    Webinar = 2
}

/// <summary>
/// Represents a video conference meeting
/// </summary>
[Table("Meetings")]
public class Meeting
{
    /// <summary>
    /// Unique identifier for the meeting
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    /// <summary>
    /// Meeting title/subject
    /// </summary>
    [Required]
    [StringLength(200)]
    [Column(TypeName = "varchar(200)")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    [StringLength(1000)]
    [Column(TypeName = "text")]
    public string? Description { get; set; }

    /// <summary>
    /// Unique meeting code for joining (e.g., "123-456-789")
    /// </summary>
    [Required]
    [StringLength(20)]
    [Column(TypeName = "varchar(20)")]
    public string MeetingCode { get; set; } = string.Empty;

    /// <summary>
    /// Unique invitation link for the meeting
    /// </summary>
    [Required]
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string InvitationLink { get; set; } = string.Empty;

    /// <summary>
    /// Meeting password (optional)
    /// </summary>
    [StringLength(50)]
    [Column(TypeName = "varchar(50)")]
    public string? Password { get; set; }

    /// <summary>
    /// Type of meeting (OneOnOne, Group, Webinar)
    /// </summary>
    [Required]
    public MeetingType Type { get; set; } = MeetingType.Group;

    /// <summary>
    /// Current status of the meeting
    /// </summary>
    [Required]
    public MeetingStatus Status { get; set; } = MeetingStatus.Scheduled;

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    [Required]
    public int MaxParticipants { get; set; } = 100;

    /// <summary>
    /// Whether the meeting is recurring
    /// </summary>
    [Required]
    public bool IsRecurring { get; set; } = false;

    /// <summary>
    /// Recurrence pattern (if recurring)
    /// </summary>
    [StringLength(100)]
    [Column(TypeName = "varchar(100)")]
    public string? RecurrencePattern { get; set; }

    /// <summary>
    /// Scheduled start time
    /// </summary>
    [Required]
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    [Required]
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Actual start time (when meeting actually started)
    /// </summary>
    public DateTime? ActualStartTime { get; set; }

    /// <summary>
    /// Actual end time (when meeting actually ended)
    /// </summary>
    public DateTime? ActualEndTime { get; set; }

    /// <summary>
    /// Meeting settings as JSON
    /// </summary>
    [Column(TypeName = "json")]
    public string? Settings { get; set; }

    /// <summary>
    /// Whether recording is enabled for this meeting
    /// </summary>
    [Required]
    public bool IsRecordingEnabled { get; set; } = false;

    /// <summary>
    /// URL to meeting recording (if available)
    /// </summary>
    [StringLength(500)]
    [Column(TypeName = "varchar(500)")]
    public string? RecordingUrl { get; set; }

    /// <summary>
    /// When the meeting was created
    /// </summary>
    [Required]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// When the meeting was last updated
    /// </summary>
    [Required]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign Keys

    /// <summary>
    /// ID of the user who hosts/created this meeting
    /// </summary>
    [Required]
    public int HostUserId { get; set; }

    // Navigation Properties

    /// <summary>
    /// The user who hosts this meeting
    /// </summary>
    [ForeignKey(nameof(HostUserId))]
    public virtual User HostUser { get; set; } = null!;

    /// <summary>
    /// Participants in this meeting
    /// </summary>
    public virtual ICollection<MeetingParticipant> Participants { get; set; } = new List<MeetingParticipant>();

    /// <summary>
    /// Invitations for this meeting
    /// </summary>
    public virtual ICollection<Invitation> Invitations { get; set; } = new List<Invitation>();

    /// <summary>
    /// Chat messages in this meeting
    /// </summary>
    public virtual ICollection<ChatMessage> ChatMessages { get; set; } = new List<ChatMessage>();

    /// <summary>
    /// Screen sharing sessions in this meeting
    /// </summary>
    public virtual ICollection<ScreenSharingMetadata> ScreenSharingSessions { get; set; } = new List<ScreenSharingMetadata>();
}
