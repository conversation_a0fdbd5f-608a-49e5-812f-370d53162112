@model GMCadiomMeeting.Web.Models.RegisterViewModel
@{
    ViewData["Title"] = "Register";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h2 class="h4 mb-2">Create Your Account</h2>
                        <p class="text-muted">Join GMCadiom Meeting and start connecting with others</p>
                    </div>

                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FirstName" class="form-label">First Name</label>
                                <input asp-for="FirstName" class="form-control" placeholder="Enter your first name" />
                                <span asp-validation-for="FirstName" class="text-danger small"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="LastName" class="form-label">Last Name</label>
                                <input asp-for="LastName" class="form-control" placeholder="Enter your last name" />
                                <span asp-validation-for="LastName" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="DisplayName" class="form-label">Display Name <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input asp-for="DisplayName" class="form-control" placeholder="How others will see you" />
                            </div>
                            <span asp-validation-for="DisplayName" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email Address <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input asp-for="Email" class="form-control" placeholder="Enter your email address" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">Password <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input asp-for="Password" class="form-control" placeholder="Create a strong password" />
                            </div>
                            <span asp-validation-for="Password" class="text-danger small"></span>
                            <div class="form-text">Password must be at least 6 characters long.</div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="TimeZone" class="form-label">Time Zone</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <select asp-for="TimeZone" class="form-select">
                                    <option value="">Select your time zone</option>
                                    <option value="America/New_York">Eastern Time (ET)</option>
                                    <option value="America/Chicago">Central Time (CT)</option>
                                    <option value="America/Denver">Mountain Time (MT)</option>
                                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                                    <option value="America/Anchorage">Alaska Time (AKT)</option>
                                    <option value="Pacific/Honolulu">Hawaii Time (HT)</option>
                                    <option value="UTC">UTC</option>
                                    <option value="Europe/London">London (GMT)</option>
                                    <option value="Europe/Paris">Paris (CET)</option>
                                    <option value="Europe/Berlin">Berlin (CET)</option>
                                    <option value="Asia/Tokyo">Tokyo (JST)</option>
                                    <option value="Asia/Shanghai">Shanghai (CST)</option>
                                    <option value="Asia/Kolkata">Mumbai (IST)</option>
                                    <option value="Australia/Sydney">Sydney (AEDT)</option>
                                </select>
                            </div>
                            <span asp-validation-for="TimeZone" class="text-danger small"></span>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                                    <a href="#" class="text-decoration-none">Privacy Policy</a>
                                </label>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">
                                Already have an account? 
                                <a asp-action="Login" class="text-decoration-none">Sign in here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted">
                    <a asp-controller="Meeting" asp-action="Join" class="text-decoration-none">
                        <i class="fas fa-users me-1"></i>Join a meeting as guest
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
