using GMCadiomMeeting.Data.Models;

namespace GMCadiomMeeting.Api.Services.Interfaces;

/// <summary>
/// Interface for user business logic operations
/// </summary>
public interface IUserService
{
    /// <summary>
    /// Register a new user with validation
    /// </summary>
    Task<User> RegisterUserAsync(RegisterUserDto dto);

    /// <summary>
    /// Authenticate user login
    /// </summary>
    Task<(User user, string token)> AuthenticateAsync(string email, string password);

    /// <summary>
    /// Get user by ID
    /// </summary>
    Task<User?> GetUserByIdAsync(int userId);

    /// <summary>
    /// Get user by email
    /// </summary>
    Task<User?> GetUserByEmailAsync(string email);

    /// <summary>
    /// Update user profile
    /// </summary>
    Task<User> UpdateUserAsync(int userId, UpdateUserDto dto);

    /// <summary>
    /// Change user password
    /// </summary>
    Task ChangePasswordAsync(int userId, string currentPassword, string newPassword);

    /// <summary>
    /// Search users by query
    /// </summary>
    Task<IEnumerable<User>> SearchUsersAsync(string query, int limit = 10);

    /// <summary>
    /// Verify email address
    /// </summary>
    Task VerifyEmailAsync(int userId, string verificationToken);

    /// <summary>
    /// Deactivate user account
    /// </summary>
    Task DeactivateUserAsync(int userId);

    /// <summary>
    /// Generate password reset token
    /// </summary>
    Task<string> GeneratePasswordResetTokenAsync(string email);

    /// <summary>
    /// Reset password using token
    /// </summary>
    Task ResetPasswordAsync(string email, string token, string newPassword);

    /// <summary>
    /// Hash password with salt
    /// </summary>
    (string hash, string salt) HashPassword(string password);

    /// <summary>
    /// Verify password against hash
    /// </summary>
    bool VerifyPassword(string password, string hash, string salt);
}

// DTOs for user service
public class RegisterUserDto
{
    public string Email { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? TimeZone { get; set; }
}

public class UpdateUserDto
{
    public string? DisplayName { get; set; }
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public string? TimeZone { get; set; }
}
