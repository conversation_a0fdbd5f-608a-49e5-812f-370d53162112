using Microsoft.EntityFrameworkCore;
using GMCadiomMeeting.Data.Models;

namespace GMCadiomMeeting.Data.Context;

/// <summary>
/// Main database context for the GMCadiom Meeting application
/// </summary>
public class GMCadiomMeetingDbContext : DbContext
{
    public GMCadiomMeetingDbContext(DbContextOptions<GMCadiomMeetingDbContext> options) : base(options)
    {
    }

    // DbSets for all entities
    public DbSet<User> Users { get; set; }
    public DbSet<Meeting> Meetings { get; set; }
    public DbSet<MeetingParticipant> MeetingParticipants { get; set; }
    public DbSet<Invitation> Invitations { get; set; }
    public DbSet<ChatMessage> ChatMessages { get; set; }
    public DbSet<ScreenSharingMetadata> ScreenSharingMetadata { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure User entity
        ConfigureUser(modelBuilder);

        // Configure Meeting entity
        ConfigureMeeting(modelBuilder);

        // Configure MeetingParticipant entity
        ConfigureMeetingParticipant(modelBuilder);

        // Configure Invitation entity
        ConfigureInvitation(modelBuilder);

        // Configure ChatMessage entity
        ConfigureChatMessage(modelBuilder);

        // Configure ScreenSharingMetadata entity
        ConfigureScreenSharingMetadata(modelBuilder);

        // Configure indexes for performance
        ConfigureIndexes(modelBuilder);
    }

    private void ConfigureUser(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<User>();

        // Unique constraints
        entity.HasIndex(u => u.Email).IsUnique();

        // Default values
        entity.Property(u => u.IsActive).HasDefaultValue(true);
        entity.Property(u => u.IsEmailVerified).HasDefaultValue(false);
        entity.Property(u => u.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(u => u.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    }

    private void ConfigureMeeting(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<Meeting>();

        // Unique constraints
        entity.HasIndex(m => m.MeetingCode).IsUnique();
        entity.HasIndex(m => m.InvitationLink).IsUnique();

        // Default values
        entity.Property(m => m.Type).HasDefaultValue(MeetingType.Group);
        entity.Property(m => m.Status).HasDefaultValue(MeetingStatus.Scheduled);
        entity.Property(m => m.MaxParticipants).HasDefaultValue(100);
        entity.Property(m => m.IsRecurring).HasDefaultValue(false);
        entity.Property(m => m.IsRecordingEnabled).HasDefaultValue(false);
        entity.Property(m => m.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(m => m.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

        // Relationships
        entity.HasOne(m => m.HostUser)
              .WithMany(u => u.HostedMeetings)
              .HasForeignKey(m => m.HostUserId)
              .OnDelete(DeleteBehavior.Restrict);
    }

    private void ConfigureMeetingParticipant(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<MeetingParticipant>();

        // Composite unique constraint (one participation record per user per meeting)
        entity.HasIndex(mp => new { mp.UserId, mp.MeetingId }).IsUnique();

        // Default values
        entity.Property(mp => mp.Role).HasDefaultValue(ParticipantRole.Attendee);
        entity.Property(mp => mp.Status).HasDefaultValue(ConnectionStatus.Disconnected);
        entity.Property(mp => mp.IsCameraEnabled).HasDefaultValue(false);
        entity.Property(mp => mp.IsMicrophoneEnabled).HasDefaultValue(false);
        entity.Property(mp => mp.IsScreenSharing).HasDefaultValue(false);
        entity.Property(mp => mp.CanShareScreen).HasDefaultValue(true);
        entity.Property(mp => mp.CanUseChat).HasDefaultValue(true);
        entity.Property(mp => mp.CanUnmute).HasDefaultValue(true);
        entity.Property(mp => mp.CanTurnOnCamera).HasDefaultValue(true);
        entity.Property(mp => mp.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(mp => mp.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

        // Relationships
        entity.HasOne(mp => mp.User)
              .WithMany(u => u.MeetingParticipations)
              .HasForeignKey(mp => mp.UserId)
              .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(mp => mp.Meeting)
              .WithMany(m => m.Participants)
              .HasForeignKey(mp => mp.MeetingId)
              .OnDelete(DeleteBehavior.Cascade);
    }

    private void ConfigureInvitation(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<Invitation>();

        // Unique constraints
        entity.HasIndex(i => i.InvitationToken).IsUnique();

        // Default values
        entity.Property(i => i.Status).HasDefaultValue(InvitationStatus.Pending);
        entity.Property(i => i.Method).HasDefaultValue(InvitationMethod.Email);
        entity.Property(i => i.InvitedRole).HasDefaultValue(ParticipantRole.Attendee);
        entity.Property(i => i.SentAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(i => i.ResendCount).HasDefaultValue(0);
        entity.Property(i => i.AllowJoinBeforeHost).HasDefaultValue(false);
        entity.Property(i => i.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(i => i.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

        // Relationships
        entity.HasOne(i => i.Meeting)
              .WithMany(m => m.Invitations)
              .HasForeignKey(i => i.MeetingId)
              .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(i => i.SentByUser)
              .WithMany(u => u.SentInvitations)
              .HasForeignKey(i => i.SentByUserId)
              .OnDelete(DeleteBehavior.Restrict);

        entity.HasOne(i => i.InvitedUser)
              .WithMany(u => u.ReceivedInvitations)
              .HasForeignKey(i => i.InvitedUserId)
              .OnDelete(DeleteBehavior.SetNull);
    }

    private void ConfigureChatMessage(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ChatMessage>();

        // Default values
        entity.Property(cm => cm.Type).HasDefaultValue(MessageType.Text);
        entity.Property(cm => cm.Scope).HasDefaultValue(MessageScope.Public);
        entity.Property(cm => cm.IsEdited).HasDefaultValue(false);
        entity.Property(cm => cm.IsDeleted).HasDefaultValue(false);
        entity.Property(cm => cm.SentAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Relationships
        entity.HasOne(cm => cm.Meeting)
              .WithMany(m => m.ChatMessages)
              .HasForeignKey(cm => cm.MeetingId)
              .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(cm => cm.Sender)
              .WithMany(u => u.ChatMessages)
              .HasForeignKey(cm => cm.SenderId)
              .OnDelete(DeleteBehavior.Restrict);

        entity.HasOne(cm => cm.Recipient)
              .WithMany()
              .HasForeignKey(cm => cm.RecipientId)
              .OnDelete(DeleteBehavior.SetNull);

        entity.HasOne(cm => cm.ReplyToMessage)
              .WithMany(cm => cm.Replies)
              .HasForeignKey(cm => cm.ReplyToMessageId)
              .OnDelete(DeleteBehavior.SetNull);
    }

    private void ConfigureScreenSharingMetadata(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ScreenSharingMetadata>();

        // Unique constraints
        entity.HasIndex(ssm => ssm.SessionId).IsUnique();

        // Default values
        entity.Property(ssm => ssm.Type).HasDefaultValue(ScreenSharingType.FullScreen);
        entity.Property(ssm => ssm.Status).HasDefaultValue(ScreenSharingStatus.Starting);
        entity.Property(ssm => ssm.Quality).HasDefaultValue(ScreenSharingQuality.Medium);
        entity.Property(ssm => ssm.IsAudioShared).HasDefaultValue(false);
        entity.Property(ssm => ssm.HasControlPermission).HasDefaultValue(false);
        entity.Property(ssm => ssm.AllowControlRequests).HasDefaultValue(false);
        entity.Property(ssm => ssm.IsRecorded).HasDefaultValue(false);
        entity.Property(ssm => ssm.StartedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(ssm => ssm.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
        entity.Property(ssm => ssm.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

        // Relationships
        entity.HasOne(ssm => ssm.Meeting)
              .WithMany(m => m.ScreenSharingSessions)
              .HasForeignKey(ssm => ssm.MeetingId)
              .OnDelete(DeleteBehavior.Cascade);

        entity.HasOne(ssm => ssm.SharingUser)
              .WithMany(u => u.ScreenSharingSessions)
              .HasForeignKey(ssm => ssm.SharingUserId)
              .OnDelete(DeleteBehavior.Restrict);
    }

    private void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Performance indexes for common queries

        // User indexes
        modelBuilder.Entity<User>()
            .HasIndex(u => u.IsActive);
        modelBuilder.Entity<User>()
            .HasIndex(u => u.CreatedAt);

        // Meeting indexes
        modelBuilder.Entity<Meeting>()
            .HasIndex(m => m.Status);
        modelBuilder.Entity<Meeting>()
            .HasIndex(m => m.ScheduledStartTime);
        modelBuilder.Entity<Meeting>()
            .HasIndex(m => new { m.HostUserId, m.Status });

        // MeetingParticipant indexes
        modelBuilder.Entity<MeetingParticipant>()
            .HasIndex(mp => mp.Status);
        modelBuilder.Entity<MeetingParticipant>()
            .HasIndex(mp => new { mp.MeetingId, mp.Status });

        // Invitation indexes
        modelBuilder.Entity<Invitation>()
            .HasIndex(i => i.Status);
        modelBuilder.Entity<Invitation>()
            .HasIndex(i => i.InviteeEmail);
        modelBuilder.Entity<Invitation>()
            .HasIndex(i => new { i.MeetingId, i.Status });

        // ChatMessage indexes
        modelBuilder.Entity<ChatMessage>()
            .HasIndex(cm => new { cm.MeetingId, cm.SentAt });
        modelBuilder.Entity<ChatMessage>()
            .HasIndex(cm => new { cm.MeetingId, cm.Scope });

        // ScreenSharingMetadata indexes
        modelBuilder.Entity<ScreenSharingMetadata>()
            .HasIndex(ssm => new { ssm.MeetingId, ssm.Status });
        modelBuilder.Entity<ScreenSharingMetadata>()
            .HasIndex(ssm => ssm.StartedAt);
    }
}
