@model GMCadiomMeeting.Web.Models.JoinMeetingViewModel
@{
    ViewData["Title"] = "Join Meeting";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-video fa-3x text-primary mb-3"></i>
                        <h2 class="h4 mb-2">Join a Meeting</h2>
                        <p class="text-muted">Enter your meeting details to join instantly</p>
                    </div>

                    <form asp-action="Join" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="MeetingCode" class="form-label">Meeting Code <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                                <input asp-for="MeetingCode" class="form-control" placeholder="123-456-789" />
                            </div>
                            <span asp-validation-for="MeetingCode" class="text-danger small"></span>
                            <div class="form-text">Enter the meeting code provided by the host</div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="DisplayName" class="form-label">Your Name</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input asp-for="DisplayName" class="form-control" placeholder="Enter your name" />
                            </div>
                            <span asp-validation-for="DisplayName" class="text-danger small"></span>
                            <div class="form-text">How you'll appear to other participants</div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">Meeting Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input asp-for="Password" class="form-control" placeholder="Enter password if required" />
                            </div>
                            <span asp-validation-for="Password" class="text-danger small"></span>
                            <div class="form-text">Leave blank if the meeting doesn't require a password</div>
                        </div>

                        <!-- Media Preview Section -->
                        <div class="mb-4">
                            <h6 class="mb-3">Media Settings</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center p-3">
                                            <div id="videoPreview" class="mb-3" style="height: 120px; background: #000; border-radius: 8px; position: relative;">
                                                <video id="previewVideo" autoplay muted style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;"></video>
                                                <div id="videoPlaceholder" class="d-flex align-items-center justify-content-center h-100 text-white">
                                                    <i class="fas fa-video-slash fa-2x"></i>
                                                </div>
                                            </div>
                                            <div class="form-check">
                                                <input asp-for="IsCameraEnabled" class="form-check-input" id="cameraToggle" />
                                                <label class="form-check-label" for="cameraToggle">
                                                    <i class="fas fa-video me-1"></i>Enable Camera
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center p-3">
                                            <div class="mb-3" style="height: 120px;">
                                                <div class="d-flex align-items-center justify-content-center h-100">
                                                    <div id="audioIndicator" class="text-center">
                                                        <i class="fas fa-microphone fa-3x text-muted mb-2"></i>
                                                        <div class="progress" style="height: 8px;">
                                                            <div id="audioLevel" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-check">
                                                <input asp-for="IsMicrophoneEnabled" class="form-check-input" id="microphoneToggle" />
                                                <label class="form-check-label" for="microphoneToggle">
                                                    <i class="fas fa-microphone me-1"></i>Enable Microphone
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-video me-2"></i>Join Meeting
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0 text-muted">
                                Don't have a meeting code? 
                                <a asp-controller="Account" asp-action="Login" class="text-decoration-none">Sign in</a> 
                                to create or schedule meetings
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-question-circle text-info me-2"></i>Need Help?
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Meeting codes are usually 9-12 digits
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    You can join with or without an account
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Test your camera and microphone first
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Contact the host if you have issues
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Media preview functionality
        let videoStream = null;
        let audioStream = null;

        document.addEventListener('DOMContentLoaded', function() {
            const cameraToggle = document.getElementById('cameraToggle');
            const microphoneToggle = document.getElementById('microphoneToggle');
            const previewVideo = document.getElementById('previewVideo');
            const videoPlaceholder = document.getElementById('videoPlaceholder');
            const audioIndicator = document.getElementById('audioIndicator');

            // Camera toggle
            cameraToggle.addEventListener('change', async function() {
                if (this.checked) {
                    try {
                        videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
                        previewVideo.srcObject = videoStream;
                        previewVideo.style.display = 'block';
                        videoPlaceholder.style.display = 'none';
                    } catch (error) {
                        console.error('Error accessing camera:', error);
                        this.checked = false;
                        alert('Unable to access camera. Please check your permissions.');
                    }
                } else {
                    if (videoStream) {
                        videoStream.getTracks().forEach(track => track.stop());
                        videoStream = null;
                    }
                    previewVideo.style.display = 'none';
                    videoPlaceholder.style.display = 'flex';
                }
            });

            // Microphone toggle
            microphoneToggle.addEventListener('change', async function() {
                if (this.checked) {
                    try {
                        audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        // You could add audio level visualization here
                        audioIndicator.querySelector('i').className = 'fas fa-microphone fa-3x text-success mb-2';
                    } catch (error) {
                        console.error('Error accessing microphone:', error);
                        this.checked = false;
                        alert('Unable to access microphone. Please check your permissions.');
                    }
                } else {
                    if (audioStream) {
                        audioStream.getTracks().forEach(track => track.stop());
                        audioStream = null;
                    }
                    audioIndicator.querySelector('i').className = 'fas fa-microphone-slash fa-3x text-muted mb-2';
                }
            });

            // Cleanup on page unload
            window.addEventListener('beforeunload', function() {
                if (videoStream) {
                    videoStream.getTracks().forEach(track => track.stop());
                }
                if (audioStream) {
                    audioStream.getTracks().forEach(track => track.stop());
                }
            });
        });
    </script>
}
