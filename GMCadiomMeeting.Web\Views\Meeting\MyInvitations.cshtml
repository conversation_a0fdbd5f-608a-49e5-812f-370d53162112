@model IEnumerable<GMCadiomMeeting.Web.Models.InvitationViewModel>
@{
    ViewData["Title"] = "My Invitations";
    var pendingInvitations = Model.Where(i => i.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Pending);
    var respondedInvitations = Model.Where(i => i.Status != GMCadiomMeeting.Web.Models.InvitationStatus.Pending);
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">My Invitations</h1>
                    <p class="text-muted mb-0">Manage your meeting invitations</p>
                </div>
                <div>
                    <a asp-controller="Home" asp-action="Dashboard" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Invitation Stats -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-warning">@pendingInvitations.Count()</div>
                            <div class="text-muted small">Pending</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-check"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-success">@Model.Count(i => i.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Accepted)</div>
                            <div class="text-muted small">Accepted</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-danger">@Model.Count(i => i.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Declined)</div>
                            <div class="text-muted small">Declined</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-info">@Model.Count()</div>
                            <div class="text-muted small">Total</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Invitations -->
    @if (pendingInvitations.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-warning text-white py-3">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Pending Invitations (@pendingInvitations.Count())
                        </h5>
                    </div>
                    <div class="card-body">
                        @foreach (var invitation in pendingInvitations.OrderBy(i => i.MeetingScheduledStart))
                        {
                            <div class="card border mb-3">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-lg-8">
                                            <h6 class="mb-2">@invitation.MeetingTitle</h6>
                                            <div class="text-muted small mb-2">
                                                <i class="fas fa-user me-1"></i>From: @invitation.SentByUser
                                                <span class="mx-2">•</span>
                                                <i class="fas fa-calendar me-1"></i>@invitation.MeetingScheduledStart?.ToString("MMM dd, yyyy h:mm tt")
                                                <span class="mx-2">•</span>
                                                <span class="badge bg-@(invitation.InvitedRole == GMCadiomMeeting.Web.Models.ParticipantRole.Host ? "primary" : 
                                                                      invitation.InvitedRole == GMCadiomMeeting.Web.Models.ParticipantRole.Presenter ? "warning" : "secondary")">
                                                    @invitation.InvitedRole
                                                </span>
                                            </div>
                                            @if (!string.IsNullOrEmpty(invitation.PersonalMessage))
                                            {
                                                <div class="alert alert-light mb-2">
                                                    <small><i class="fas fa-quote-left me-1"></i>@invitation.PersonalMessage</small>
                                                </div>
                                            }
                                            <div class="text-muted small">
                                                <i class="fas fa-paper-plane me-1"></i>Sent: @invitation.SentAt.ToString("MMM dd, yyyy h:mm tt")
                                                @if (invitation.ExpiresAt.HasValue)
                                                {
                                                    <span class="mx-2">•</span>
                                                    <i class="fas fa-hourglass-end me-1"></i><text>Expires: @invitation.ExpiresAt.Value.ToString("MMM dd, yyyy h:mm tt")</text>
                                                }
                                            </div>
                                        </div>
                                        <div class="col-lg-4 text-end">
                                            <div class="btn-group">
                                                <form asp-action="RespondToInvitation" method="post" class="d-inline">
                                                    <input type="hidden" name="invitationId" value="@invitation.Id" />
                                                    <input type="hidden" name="accept" value="true" />
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-check me-1"></i>Accept
                                                    </button>
                                                </form>
                                                <form asp-action="RespondToInvitation" method="post" class="d-inline">
                                                    <input type="hidden" name="invitationId" value="@invitation.Id" />
                                                    <input type="hidden" name="accept" value="false" />
                                                    <button type="submit" class="btn btn-outline-danger">
                                                        <i class="fas fa-times me-1"></i>Decline
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- All Invitations -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope text-info me-2"></i>All Invitations
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Meeting</th>
                                        <th>From</th>
                                        <th>Role</th>
                                        <th>Scheduled</th>
                                        <th>Status</th>
                                        <th>Sent</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invitation in Model.OrderByDescending(i => i.SentAt))
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-semibold">@invitation.MeetingTitle</div>
                                                @if (!string.IsNullOrEmpty(invitation.PersonalMessage))
                                                {
                                                    <div class="small text-muted">@invitation.PersonalMessage</div>
                                                }
                                            </td>
                                            <td>@invitation.SentByUser</td>
                                            <td>
                                                <span class="badge bg-@(invitation.InvitedRole == GMCadiomMeeting.Web.Models.ParticipantRole.Host ? "primary" : 
                                                                      invitation.InvitedRole == GMCadiomMeeting.Web.Models.ParticipantRole.Presenter ? "warning" : "secondary")">
                                                    @invitation.InvitedRole
                                                </span>
                                            </td>
                                            <td>
                                                @if (invitation.MeetingScheduledStart.HasValue)
                                                {
                                                    <div>@invitation.MeetingScheduledStart.Value.ToString("MMM dd, yyyy")</div>
                                                    <div class="small text-muted">@invitation.MeetingScheduledStart.Value.ToString("h:mm tt")</div>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-@(invitation.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Pending ? "warning" : 
                                                                      invitation.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Accepted ? "success" : 
                                                                      invitation.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Declined ? "danger" : "secondary")">
                                                    @invitation.Status
                                                </span>
                                                @if (invitation.RespondedAt.HasValue)
                                                {
                                                    <div class="small text-muted">@invitation.RespondedAt.Value.ToString("MMM dd")</div>
                                                }
                                            </td>
                                            <td>
                                                <div>@invitation.SentAt.ToString("MMM dd, yyyy")</div>
                                                <div class="small text-muted">@invitation.SentAt.ToString("h:mm tt")</div>
                                            </td>
                                            <td>
                                                @if (invitation.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Pending)
                                                {
                                                    <div class="btn-group btn-group-sm">
                                                        <form asp-action="RespondToInvitation" method="post" class="d-inline">
                                                            <input type="hidden" name="invitationId" value="@invitation.Id" />
                                                            <input type="hidden" name="accept" value="true" />
                                                            <button type="submit" class="btn btn-success btn-sm">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                        <form asp-action="RespondToInvitation" method="post" class="d-inline">
                                                            <input type="hidden" name="invitationId" value="@invitation.Id" />
                                                            <input type="hidden" name="accept" value="false" />
                                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                }
                                                else if (invitation.Status == GMCadiomMeeting.Web.Models.InvitationStatus.Accepted)
                                                {
                                                    <span class="text-success small">
                                                        <i class="fas fa-check-circle me-1"></i>Accepted
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted small">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Invitations</h5>
                            <p class="text-muted mb-0">You haven't received any meeting invitations yet.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
