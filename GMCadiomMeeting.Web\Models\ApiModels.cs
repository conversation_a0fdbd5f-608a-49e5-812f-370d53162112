using System.ComponentModel.DataAnnotations;

namespace GMCadiomMeeting.Web.Models;

// User Models
public class UserViewModel
{
    public int Id { get; set; }
    public string Email { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? ProfilePictureUrl { get; set; }
    public string? TimeZone { get; set; }
    public bool IsActive { get; set; }
    public bool IsEmailVerified { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class LoginViewModel
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [DataType(DataType.Password)]
    public string Password { get; set; } = string.Empty;

    public bool RememberMe { get; set; }
}

public class RegisterViewModel
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [DataType(DataType.Password)]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = string.Empty;

    [Required]
    [Display(Name = "Display Name")]
    public string DisplayName { get; set; } = string.Empty;

    [Display(Name = "First Name")]
    public string? FirstName { get; set; }

    [Display(Name = "Last Name")]
    public string? LastName { get; set; }

    [Display(Name = "Time Zone")]
    public string? TimeZone { get; set; }
}

public class LoginResponse
{
    public UserViewModel User { get; set; } = null!;
    public string Token { get; set; } = string.Empty;
    public DateTime ExpiresAt { get; set; }
}

// Meeting Models
public class MeetingViewModel
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string MeetingCode { get; set; } = string.Empty;
    public string InvitationLink { get; set; } = string.Empty;
    public string? Password { get; set; }
    public MeetingType Type { get; set; }
    public MeetingStatus Status { get; set; }
    public int MaxParticipants { get; set; }
    public bool IsRecurring { get; set; }
    public string? RecurrencePattern { get; set; }
    public DateTime ScheduledStartTime { get; set; }
    public DateTime ScheduledEndTime { get; set; }
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public bool IsRecordingEnabled { get; set; }
    public string? RecordingUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int HostUserId { get; set; }
    public UserViewModel? HostUser { get; set; }
    public List<MeetingParticipantViewModel> Participants { get; set; } = new();
}

public class CreateMeetingViewModel
{
    [Required]
    [Display(Name = "Meeting Title")]
    public string Title { get; set; } = string.Empty;

    [Display(Name = "Description")]
    public string? Description { get; set; }

    [Required]
    [Display(Name = "Meeting Type")]
    public MeetingType Type { get; set; } = MeetingType.Group;

    [Required]
    [Display(Name = "Start Time")]
    public DateTime ScheduledStartTime { get; set; } = DateTime.Now.AddHours(1);

    [Required]
    [Display(Name = "End Time")]
    public DateTime ScheduledEndTime { get; set; } = DateTime.Now.AddHours(2);

    [Display(Name = "Maximum Participants")]
    [Range(2, 1000)]
    public int MaxParticipants { get; set; } = 100;

    [Display(Name = "Meeting Password")]
    public string? Password { get; set; }

    [Display(Name = "Enable Recording")]
    public bool IsRecordingEnabled { get; set; } = false;

    [Display(Name = "Recurring Meeting")]
    public bool IsRecurring { get; set; } = false;

    [Display(Name = "Recurrence Pattern")]
    public string? RecurrencePattern { get; set; }
}

public class JoinMeetingViewModel
{
    [Required]
    [Display(Name = "Meeting Code")]
    public string MeetingCode { get; set; } = string.Empty;

    [Display(Name = "Your Name")]
    public string? DisplayName { get; set; }

    [Display(Name = "Meeting Password")]
    public string? Password { get; set; }

    [Display(Name = "Join with Camera On")]
    public bool IsCameraEnabled { get; set; } = false;

    [Display(Name = "Join with Microphone On")]
    public bool IsMicrophoneEnabled { get; set; } = false;
}

// Participant Models
public class MeetingParticipantViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int MeetingId { get; set; }
    public ParticipantRole Role { get; set; }
    public ConnectionStatus Status { get; set; }
    public DateTime? JoinedAt { get; set; }
    public DateTime? LeftAt { get; set; }
    public int? DurationSeconds { get; set; }
    public bool IsCameraEnabled { get; set; }
    public bool IsMicrophoneEnabled { get; set; }
    public bool IsScreenSharing { get; set; }
    public bool CanShareScreen { get; set; }
    public bool CanUseChat { get; set; }
    public bool CanUnmute { get; set; }
    public bool CanTurnOnCamera { get; set; }
    public string? DisplayNameInMeeting { get; set; }
    public UserViewModel? User { get; set; }
}

// Invitation Models
public class InvitationViewModel
{
    public int Id { get; set; }
    public int MeetingId { get; set; }
    public string InvitationToken { get; set; } = string.Empty;
    public string? InviteeEmail { get; set; }
    public string? InviteeName { get; set; }
    public InvitationStatus Status { get; set; }
    public InvitationMethod Method { get; set; }
    public ParticipantRole InvitedRole { get; set; }
    public string? PersonalMessage { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public DateTime? RespondedAt { get; set; }
    public string? SentByUser { get; set; }
    public string? InvitedUser { get; set; }
    public string? MeetingTitle { get; set; }
    public DateTime? MeetingScheduledStart { get; set; }
    public string? MeetingHost { get; set; }
}

public class SendInvitationViewModel
{
    [Required]
    public int MeetingId { get; set; }

    [Required]
    [Display(Name = "Invitee Emails (one per line)")]
    public string InviteeEmails { get; set; } = string.Empty;

    [Display(Name = "Personal Message")]
    public string? PersonalMessage { get; set; }

    [Display(Name = "Invitation Role")]
    public ParticipantRole InvitedRole { get; set; } = ParticipantRole.Attendee;

    [Display(Name = "Expires At")]
    public DateTime? ExpiresAt { get; set; }

    [Display(Name = "Allow Join Before Host")]
    public bool AllowJoinBeforeHost { get; set; } = false;
}

// Chat Models
public class ChatMessageViewModel
{
    public int Id { get; set; }
    public int MeetingId { get; set; }
    public int SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public int? RecipientId { get; set; }
    public string? RecipientName { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType Type { get; set; }
    public MessageScope Scope { get; set; }
    public bool IsEdited { get; set; }
    public bool IsDeleted { get; set; }
    public string? FileUrl { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileMimeType { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? EditedAt { get; set; }
    public int? ReplyToMessageId { get; set; }
}

// Enums (matching the API)
public enum MeetingType
{
    OneOnOne = 0,
    Group = 1,
    Webinar = 2
}

public enum MeetingStatus
{
    Scheduled = 0,
    InProgress = 1,
    Ended = 2,
    Cancelled = 3
}

public enum ParticipantRole
{
    Host = 0,
    CoHost = 1,
    Presenter = 2,
    Attendee = 3,
    Observer = 4
}

public enum ConnectionStatus
{
    Connected = 0,
    Disconnected = 1,
    Reconnecting = 2,
    Left = 3
}

public enum InvitationStatus
{
    Pending = 0,
    Accepted = 1,
    Declined = 2,
    Expired = 3,
    Cancelled = 4
}

public enum InvitationMethod
{
    Email = 0,
    Link = 1,
    SMS = 2,
    InApp = 3
}

public enum MessageType
{
    Text = 0,
    File = 1,
    Image = 2,
    System = 3,
    Emoji = 4,
    Link = 5
}

public enum MessageScope
{
    Public = 0,
    Private = 1,
    Hosts = 2,
    Presenters = 3
}
