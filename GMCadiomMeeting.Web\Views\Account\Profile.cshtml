@model GMCadiomMeeting.Web.Models.UserViewModel
@{
    ViewData["Title"] = "User Profile";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white py-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-white text-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                @if (!string.IsNullOrEmpty(Model.ProfilePictureUrl))
                                {
                                    <img src="@Model.ProfilePictureUrl" alt="Profile" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                                }
                                else
                                {
                                    <i class="fas fa-user fa-2x"></i>
                                }
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="mb-1">@Model.DisplayName</h4>
                            <p class="mb-0 opacity-75">@Model.Email</p>
                        </div>
                        <div class="flex-shrink-0">
                            <button class="btn btn-light" onclick="enableEdit()">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form id="profileForm" method="post" asp-action="UpdateProfile">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="FirstName" value="@Model.FirstName" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="LastName" value="@Model.LastName" readonly>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Display Name</label>
                            <input type="text" class="form-control" name="DisplayName" value="@Model.DisplayName" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Email Address</label>
                            <div class="input-group">
                                <input type="email" class="form-control" value="@Model.Email" readonly>
                                <span class="input-group-text">
                                    @if (Model.IsEmailVerified)
                                    {
                                        <i class="fas fa-check-circle text-success" title="Verified"></i>
                                    }
                                    else
                                    {
                                        <i class="fas fa-exclamation-triangle text-warning" title="Not Verified"></i>
                                    }
                                </span>
                            </div>
                            @if (!Model.IsEmailVerified)
                            {
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Email not verified. <a href="#" onclick="resendVerification()">Resend verification email</a>
                                </div>
                            }
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Time Zone</label>
                            <select class="form-select" name="TimeZone" disabled>
                                <option value="">Select your time zone</option>
                                <option value="America/New_York" selected="@(Model.TimeZone == "America/New_York")">Eastern Time (ET)</option>
                                <option value="America/Chicago" selected="@(Model.TimeZone == "America/Chicago")">Central Time (CT)</option>
                                <option value="America/Denver" selected="@(Model.TimeZone == "America/Denver")">Mountain Time (MT)</option>
                                <option value="America/Los_Angeles" selected="@(Model.TimeZone == "America/Los_Angeles")">Pacific Time (PT)</option>
                                <option value="America/Anchorage" selected="@(Model.TimeZone == "America/Anchorage")">Alaska Time (AKT)</option>
                                <option value="Pacific/Honolulu" selected="@(Model.TimeZone == "Pacific/Honolulu")">Hawaii Time (HT)</option>
                                <option value="UTC" selected="@(Model.TimeZone == "UTC")">UTC</option>
                                <option value="Europe/London" selected="@(Model.TimeZone == "Europe/London")">London (GMT)</option>
                                <option value="Europe/Paris" selected="@(Model.TimeZone == "Europe/Paris")">Paris (CET)</option>
                                <option value="Europe/Berlin" selected="@(Model.TimeZone == "Europe/Berlin")">Berlin (CET)</option>
                                <option value="Asia/Tokyo" selected="@(Model.TimeZone == "Asia/Tokyo")">Tokyo (JST)</option>
                                <option value="Asia/Shanghai" selected="@(Model.TimeZone == "Asia/Shanghai")">Shanghai (CST)</option>
                                <option value="Asia/Kolkata" selected="@(Model.TimeZone == "Asia/Kolkata")">Mumbai (IST)</option>
                                <option value="Australia/Sydney" selected="@(Model.TimeZone == "Australia/Sydney")">Sydney (AEDT)</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Profile Picture URL</label>
                            <input type="url" class="form-control" name="ProfilePictureUrl" value="@Model.ProfilePictureUrl" readonly placeholder="https://example.com/avatar.jpg">
                            <div class="form-text">Enter a URL to your profile picture</div>
                        </div>

                        <div class="d-none" id="editButtons">
                            <hr class="my-4">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="cancelEdit()">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h6 class="mb-0">Account Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <small class="text-muted">Account Status</small>
                            <div>
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Inactive
                                    </span>
                                }
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <small class="text-muted">Member Since</small>
                            <div>@Model.CreatedAt.ToString("MMMM dd, yyyy")</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h6 class="mb-0">Security Settings</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <div class="fw-semibold">Password</div>
                            <div class="text-muted small">Change your account password</div>
                        </div>
                        <button class="btn btn-outline-primary" onclick="showChangePassword()">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-semibold">Two-Factor Authentication</div>
                            <div class="text-muted small">Add an extra layer of security to your account</div>
                        </div>
                        <button class="btn btn-outline-secondary" disabled>
                            <i class="fas fa-shield-alt me-2"></i>Coming Soon
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a asp-controller="Home" asp-action="Dashboard" class="btn btn-outline-primary w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-controller="Meeting" asp-action="Index" class="btn btn-outline-success w-100">
                                <i class="fas fa-calendar me-2"></i>My Meetings
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a asp-controller="Meeting" asp-action="MyInvitations" class="btn btn-outline-info w-100">
                                <i class="fas fa-envelope me-2"></i>Invitations
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm" method="post" asp-action="ChangePassword">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Password</label>
                        <input type="password" class="form-control" name="CurrentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" name="NewPassword" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" name="ConfirmPassword" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function enableEdit() {
            // Enable form fields
            document.querySelectorAll('#profileForm input, #profileForm select').forEach(field => {
                if (field.name !== 'Email') { // Keep email readonly
                    field.removeAttribute('readonly');
                    field.removeAttribute('disabled');
                }
            });
            
            // Show edit buttons
            document.getElementById('editButtons').classList.remove('d-none');
            
            // Hide edit button
            event.target.style.display = 'none';
        }

        function cancelEdit() {
            // Reload page to reset form
            window.location.reload();
        }

        function showChangePassword() {
            const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
            modal.show();
        }

        function resendVerification() {
            // TODO: Implement resend verification email
            showToast('Verification email sent!', 'success');
        }

        // Handle change password form
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const newPassword = this.querySelector('input[name="NewPassword"]').value;
            const confirmPassword = this.querySelector('input[name="ConfirmPassword"]').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                showToast('Passwords do not match!', 'danger');
                return false;
            }
            
            if (newPassword.length < 6) {
                e.preventDefault();
                showToast('Password must be at least 6 characters long!', 'danger');
                return false;
            }
        });
    </script>
}
