using System.ComponentModel.DataAnnotations;
using GMCadiomMeeting.Shared.Enums;

namespace GMCadiomMeeting.Shared.Models;

/// <summary>
/// Meeting participant information for API responses
/// </summary>
public class MeetingParticipantDto
{
    /// <summary>
    /// Participant unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// User ID (null for guest participants)
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// User information (if registered user)
    /// </summary>
    public UserDto? User { get; set; }

    /// <summary>
    /// Display name in the meeting
    /// </summary>
    public string? DisplayNameInMeeting { get; set; }

    /// <summary>
    /// Participant role in the meeting
    /// </summary>
    public ParticipantRole Role { get; set; }

    /// <summary>
    /// Connection status
    /// </summary>
    public ConnectionStatus Status { get; set; }

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool IsCameraEnabled { get; set; }

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool IsMicrophoneEnabled { get; set; }

    /// <summary>
    /// Indicates if participant is sharing screen
    /// </summary>
    public bool IsScreenSharing { get; set; }

    /// <summary>
    /// Time when participant joined the meeting
    /// </summary>
    public DateTime? JoinedAt { get; set; }

    /// <summary>
    /// Time when participant left the meeting
    /// </summary>
    public DateTime? LeftAt { get; set; }

    /// <summary>
    /// Total duration in the meeting (in minutes)
    /// </summary>
    public int? DurationMinutes { get; set; }

    /// <summary>
    /// Participant's IP address
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// Participant's user agent
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Date when participant record was created
    /// </summary>
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Request model for updating participant settings
/// </summary>
public class UpdateParticipantRequest
{
    /// <summary>
    /// Participant ID
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// Display name in the meeting
    /// </summary>
    [StringLength(100, ErrorMessage = "Display name cannot exceed 100 characters")]
    public string? DisplayNameInMeeting { get; set; }

    /// <summary>
    /// Participant role in the meeting
    /// </summary>
    public ParticipantRole? Role { get; set; }

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool? IsCameraEnabled { get; set; }

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool? IsMicrophoneEnabled { get; set; }
}

/// <summary>
/// Request model for removing a participant from a meeting
/// </summary>
public class RemoveParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to remove
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// Reason for removal
    /// </summary>
    [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
    public string? Reason { get; set; }
}

/// <summary>
/// Request model for muting/unmuting a participant
/// </summary>
public class MuteParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to mute/unmute
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// True to mute, false to unmute
    /// </summary>
    public bool IsMuted { get; set; }
}

/// <summary>
/// Request model for promoting a participant
/// </summary>
public class PromoteParticipantRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID to promote
    /// </summary>
    [Required(ErrorMessage = "Participant ID is required")]
    public int ParticipantId { get; set; }

    /// <summary>
    /// New role for the participant
    /// </summary>
    [Required(ErrorMessage = "New role is required")]
    public ParticipantRole NewRole { get; set; }
}

/// <summary>
/// Meeting invitation information for API responses
/// </summary>
public class InvitationDto
{
    /// <summary>
    /// Invitation unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// Meeting title
    /// </summary>
    public string? MeetingTitle { get; set; }

    /// <summary>
    /// Meeting scheduled start time
    /// </summary>
    public DateTime? MeetingScheduledStart { get; set; }

    /// <summary>
    /// User ID who sent the invitation
    /// </summary>
    public int SentByUserId { get; set; }

    /// <summary>
    /// Name of user who sent the invitation
    /// </summary>
    public string? SentByUser { get; set; }

    /// <summary>
    /// Email address of the invitee
    /// </summary>
    public string InviteeEmail { get; set; } = string.Empty;

    /// <summary>
    /// User ID of the invitee (if registered user)
    /// </summary>
    public int? InviteeUserId { get; set; }

    /// <summary>
    /// Role assigned to the invitee
    /// </summary>
    public ParticipantRole InvitedRole { get; set; }

    /// <summary>
    /// Personal message included with the invitation
    /// </summary>
    public string? PersonalMessage { get; set; }

    /// <summary>
    /// Invitation status
    /// </summary>
    public InvitationStatus Status { get; set; }

    /// <summary>
    /// Method used to send the invitation
    /// </summary>
    public InvitationMethod Method { get; set; }

    /// <summary>
    /// Date and time when invitation was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Date and time when invitation was responded to
    /// </summary>
    public DateTime? RespondedAt { get; set; }

    /// <summary>
    /// Date and time when invitation expires
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Unique token for the invitation
    /// </summary>
    public string? InvitationToken { get; set; }
}

/// <summary>
/// Request model for sending meeting invitations
/// </summary>
public class SendInvitationRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// List of email addresses to invite
    /// </summary>
    [Required(ErrorMessage = "At least one email address is required")]
    [MinLength(1, ErrorMessage = "At least one email address is required")]
    public List<string> EmailAddresses { get; set; } = new();

    /// <summary>
    /// Role to assign to invited participants
    /// </summary>
    public ParticipantRole InvitedRole { get; set; } = ParticipantRole.Attendee;

    /// <summary>
    /// Personal message to include with the invitation
    /// </summary>
    [StringLength(1000, ErrorMessage = "Personal message cannot exceed 1000 characters")]
    public string? PersonalMessage { get; set; }

    /// <summary>
    /// Method to use for sending invitations
    /// </summary>
    public InvitationMethod Method { get; set; } = InvitationMethod.Email;

    /// <summary>
    /// Expiration date for the invitations
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Send reminder before meeting
    /// </summary>
    public bool SendReminder { get; set; } = true;
}

/// <summary>
/// Request model for responding to an invitation
/// </summary>
public class RespondToInvitationRequest
{
    /// <summary>
    /// Invitation ID or token
    /// </summary>
    [Required(ErrorMessage = "Invitation ID or token is required")]
    public string InvitationIdentifier { get; set; } = string.Empty;

    /// <summary>
    /// Response to the invitation
    /// </summary>
    [Required(ErrorMessage = "Response is required")]
    public InvitationStatus Response { get; set; }

    /// <summary>
    /// Optional message with the response
    /// </summary>
    [StringLength(500, ErrorMessage = "Response message cannot exceed 500 characters")]
    public string? ResponseMessage { get; set; }
}

/// <summary>
/// Request model for getting invitations with filters
/// </summary>
public class GetInvitationsRequest : PagedRequest
{
    /// <summary>
    /// Filter by invitation status
    /// </summary>
    public InvitationStatus? Status { get; set; }

    /// <summary>
    /// Filter by meeting ID
    /// </summary>
    public int? MeetingId { get; set; }

    /// <summary>
    /// Filter by sent date range start
    /// </summary>
    public DateTime? SentAfter { get; set; }

    /// <summary>
    /// Filter by sent date range end
    /// </summary>
    public DateTime? SentBefore { get; set; }

    /// <summary>
    /// Include only invitations sent by the current user
    /// </summary>
    public bool? SentByCurrentUser { get; set; }

    /// <summary>
    /// Include only invitations received by the current user
    /// </summary>
    public bool? ReceivedByCurrentUser { get; set; }
}
