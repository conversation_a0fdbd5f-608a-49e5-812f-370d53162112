using Microsoft.EntityFrameworkCore;
using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;
using GMCadiomMeeting.Api.Services.Interfaces;

namespace GMCadiomMeeting.Api.Services;

/// <summary>
/// Service for meeting business logic operations
/// </summary>
public class MeetingService : IMeetingService
{
    private readonly GMCadiomMeetingDbContext _context;
    private readonly ILogger<MeetingService> _logger;

    public MeetingService(GMCadiomMeetingDbContext context, ILogger<MeetingService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<Meeting> CreateMeetingAsync(CreateMeetingDto dto)
    {
        // Validate host user exists
        var hostUser = await _context.Users.FindAsync(dto.HostUserId);
        if (hostUser == null || !hostUser.IsActive)
        {
            throw new ArgumentException($"Host user with ID {dto.HostUserId} not found or inactive");
        }

        // Validate scheduled times
        if (dto.ScheduledStartTime >= dto.ScheduledEndTime)
        {
            throw new ArgumentException("Scheduled start time must be before end time");
        }

        if (dto.ScheduledStartTime < DateTime.UtcNow.AddMinutes(-5))
        {
            throw new ArgumentException("Cannot schedule meetings in the past");
        }

        // Generate unique meeting code and invitation link
        var meetingCode = await GenerateUniqueMeetingCodeAsync();
        var invitationLink = $"https://meet.gmcadiom.com/join/{Guid.NewGuid()}";

        var meeting = new Meeting
        {
            Title = dto.Title,
            Description = dto.Description,
            MeetingCode = meetingCode,
            InvitationLink = invitationLink,
            Type = dto.Type,
            ScheduledStartTime = dto.ScheduledStartTime,
            ScheduledEndTime = dto.ScheduledEndTime,
            HostUserId = dto.HostUserId,
            MaxParticipants = dto.MaxParticipants,
            Password = dto.Password,
            IsRecordingEnabled = dto.IsRecordingEnabled,
            IsRecurring = dto.IsRecurring,
            RecurrencePattern = dto.RecurrencePattern,
            Status = MeetingStatus.Scheduled
        };

        _context.Meetings.Add(meeting);
        await _context.SaveChangesAsync();

        // Add host as a participant
        var hostParticipant = new MeetingParticipant
        {
            UserId = dto.HostUserId,
            MeetingId = meeting.Id,
            Role = ParticipantRole.Host,
            Status = ConnectionStatus.Disconnected,
            CanShareScreen = true,
            CanUseChat = true,
            CanUnmute = true,
            CanTurnOnCamera = true
        };

        _context.MeetingParticipants.Add(hostParticipant);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Meeting {MeetingId} created by user {UserId}", meeting.Id, dto.HostUserId);

        return meeting;
    }

    public async Task<Meeting?> GetMeetingAsync(int meetingId, int requestingUserId)
    {
        var meeting = await _context.Meetings
            .Include(m => m.HostUser)
            .Include(m => m.Participants)
                .ThenInclude(p => p.User)
            .FirstOrDefaultAsync(m => m.Id == meetingId);

        if (meeting == null)
        {
            return null;
        }

        // Check if user has access to this meeting
        if (!await CanUserAccessMeetingAsync(meetingId, requestingUserId))
        {
            throw new UnauthorizedAccessException("User does not have access to this meeting");
        }

        return meeting;
    }

    public async Task<IEnumerable<Meeting>> GetUserMeetingsAsync(int userId, MeetingStatus? status = null)
    {
        var query = _context.Meetings
            .Where(m => m.HostUserId == userId || 
                       m.Participants.Any(p => p.UserId == userId))
            .Include(m => m.HostUser)
            .Include(m => m.Participants)
                .ThenInclude(p => p.User)
            .AsQueryable();

        if (status.HasValue)
        {
            query = query.Where(m => m.Status == status.Value);
        }

        return await query
            .OrderBy(m => m.ScheduledStartTime)
            .ToListAsync();
    }

    public async Task<Meeting> UpdateMeetingAsync(int meetingId, UpdateMeetingDto dto, int requestingUserId)
    {
        var meeting = await _context.Meetings.FindAsync(meetingId);
        if (meeting == null)
        {
            throw new ArgumentException($"Meeting with ID {meetingId} not found");
        }

        // Only host can update meeting
        if (meeting.HostUserId != requestingUserId)
        {
            throw new UnauthorizedAccessException("Only the meeting host can update meeting details");
        }

        // Cannot update meetings that have ended
        if (meeting.Status == MeetingStatus.Ended)
        {
            throw new InvalidOperationException("Cannot update meetings that have ended");
        }

        // Update fields
        if (!string.IsNullOrEmpty(dto.Title))
            meeting.Title = dto.Title;

        if (dto.Description != null)
            meeting.Description = dto.Description;

        if (dto.ScheduledStartTime.HasValue)
        {
            if (dto.ScheduledStartTime.Value < DateTime.UtcNow.AddMinutes(-5))
            {
                throw new ArgumentException("Cannot schedule meetings in the past");
            }
            meeting.ScheduledStartTime = dto.ScheduledStartTime.Value;
        }

        if (dto.ScheduledEndTime.HasValue)
        {
            if (dto.ScheduledEndTime.Value <= meeting.ScheduledStartTime)
            {
                throw new ArgumentException("End time must be after start time");
            }
            meeting.ScheduledEndTime = dto.ScheduledEndTime.Value;
        }

        if (dto.MaxParticipants.HasValue)
            meeting.MaxParticipants = dto.MaxParticipants.Value;

        if (dto.Password != null)
            meeting.Password = dto.Password;

        if (dto.IsRecordingEnabled.HasValue)
            meeting.IsRecordingEnabled = dto.IsRecordingEnabled.Value;

        meeting.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Meeting {MeetingId} updated by user {UserId}", meetingId, requestingUserId);

        return meeting;
    }

    public async Task<Meeting> StartMeetingAsync(int meetingId, int hostUserId)
    {
        var meeting = await _context.Meetings.FindAsync(meetingId);
        if (meeting == null)
        {
            throw new ArgumentException($"Meeting with ID {meetingId} not found");
        }

        if (meeting.HostUserId != hostUserId)
        {
            throw new UnauthorizedAccessException("Only the meeting host can start the meeting");
        }

        if (meeting.Status != MeetingStatus.Scheduled)
        {
            throw new InvalidOperationException($"Cannot start meeting with status {meeting.Status}");
        }

        meeting.Status = MeetingStatus.InProgress;
        meeting.ActualStartTime = DateTime.UtcNow;
        meeting.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Meeting {MeetingId} started by host {UserId}", meetingId, hostUserId);

        return meeting;
    }

    public async Task<Meeting> EndMeetingAsync(int meetingId, int hostUserId)
    {
        var meeting = await _context.Meetings.FindAsync(meetingId);
        if (meeting == null)
        {
            throw new ArgumentException($"Meeting with ID {meetingId} not found");
        }

        if (meeting.HostUserId != hostUserId)
        {
            throw new UnauthorizedAccessException("Only the meeting host can end the meeting");
        }

        if (meeting.Status != MeetingStatus.InProgress)
        {
            throw new InvalidOperationException($"Cannot end meeting with status {meeting.Status}");
        }

        meeting.Status = MeetingStatus.Ended;
        meeting.ActualEndTime = DateTime.UtcNow;
        meeting.UpdatedAt = DateTime.UtcNow;

        // Update all participants to left status
        var participants = await _context.MeetingParticipants
            .Where(p => p.MeetingId == meetingId && p.Status == ConnectionStatus.Connected)
            .ToListAsync();

        foreach (var participant in participants)
        {
            participant.Status = ConnectionStatus.Left;
            participant.LeftAt = DateTime.UtcNow;
            participant.UpdatedAt = DateTime.UtcNow;

            if (participant.JoinedAt.HasValue)
            {
                participant.DurationSeconds = (int)(DateTime.UtcNow - participant.JoinedAt.Value).TotalSeconds;
            }
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("Meeting {MeetingId} ended by host {UserId}", meetingId, hostUserId);

        return meeting;
    }

    public async Task<MeetingParticipant> JoinMeetingAsync(string meetingCode, JoinMeetingDto dto)
    {
        var meeting = await _context.Meetings
            .FirstOrDefaultAsync(m => m.MeetingCode == meetingCode);

        if (meeting == null)
        {
            throw new ArgumentException($"Meeting with code {meetingCode} not found");
        }

        if (meeting.Status == MeetingStatus.Ended || meeting.Status == MeetingStatus.Cancelled)
        {
            throw new InvalidOperationException("Cannot join a meeting that has ended or been cancelled");
        }

        // Check password if required
        if (!string.IsNullOrEmpty(meeting.Password) && meeting.Password != dto.Password)
        {
            throw new UnauthorizedAccessException("Invalid meeting password");
        }

        // Check if user exists
        var user = await _context.Users.FindAsync(dto.UserId);
        if (user == null || !user.IsActive)
        {
            throw new ArgumentException($"User with ID {dto.UserId} not found or inactive");
        }

        // Check if already a participant
        var existingParticipant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meeting.Id && p.UserId == dto.UserId);

        if (existingParticipant != null)
        {
            // Update existing participant
            existingParticipant.Status = ConnectionStatus.Connected;
            existingParticipant.JoinedAt = DateTime.UtcNow;
            existingParticipant.IsCameraEnabled = dto.IsCameraEnabled;
            existingParticipant.IsMicrophoneEnabled = dto.IsMicrophoneEnabled;
            existingParticipant.IpAddress = dto.IpAddress;
            existingParticipant.UserAgent = dto.UserAgent;
            existingParticipant.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return existingParticipant;
        }

        // Check participant limit
        var currentParticipantCount = await _context.MeetingParticipants
            .CountAsync(p => p.MeetingId == meeting.Id && p.Status == ConnectionStatus.Connected);

        if (currentParticipantCount >= meeting.MaxParticipants)
        {
            throw new InvalidOperationException("Meeting has reached maximum participant limit");
        }

        // Create new participant
        var participant = new MeetingParticipant
        {
            UserId = dto.UserId,
            MeetingId = meeting.Id,
            Role = ParticipantRole.Attendee,
            Status = ConnectionStatus.Connected,
            JoinedAt = DateTime.UtcNow,
            IsCameraEnabled = dto.IsCameraEnabled,
            IsMicrophoneEnabled = dto.IsMicrophoneEnabled,
            DisplayNameInMeeting = dto.DisplayName,
            IpAddress = dto.IpAddress,
            UserAgent = dto.UserAgent
        };

        _context.MeetingParticipants.Add(participant);
        await _context.SaveChangesAsync();

        _logger.LogInformation("User {UserId} joined meeting {MeetingId}", dto.UserId, meeting.Id);

        return participant;
    }

    public async Task LeaveMeetingAsync(int meetingId, int userId)
    {
        var participant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userId);

        if (participant == null)
        {
            throw new ArgumentException("User is not a participant in this meeting");
        }

        participant.Status = ConnectionStatus.Left;
        participant.LeftAt = DateTime.UtcNow;
        participant.UpdatedAt = DateTime.UtcNow;

        if (participant.JoinedAt.HasValue)
        {
            participant.DurationSeconds = (int)(DateTime.UtcNow - participant.JoinedAt.Value).TotalSeconds;
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation("User {UserId} left meeting {MeetingId}", userId, meetingId);
    }

    public async Task<MeetingParticipant> UpdateParticipantPermissionsAsync(int meetingId, int participantUserId, UpdateParticipantPermissionsDto dto, int requestingUserId)
    {
        var requestingParticipant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == requestingUserId);

        if (requestingParticipant == null || 
            (requestingParticipant.Role != ParticipantRole.Host && requestingParticipant.Role != ParticipantRole.CoHost))
        {
            throw new UnauthorizedAccessException("Only hosts and co-hosts can update participant permissions");
        }

        var participant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == participantUserId);

        if (participant == null)
        {
            throw new ArgumentException("Participant not found in this meeting");
        }

        // Update permissions
        if (dto.Role.HasValue)
            participant.Role = dto.Role.Value;

        if (dto.CanShareScreen.HasValue)
            participant.CanShareScreen = dto.CanShareScreen.Value;

        if (dto.CanUseChat.HasValue)
            participant.CanUseChat = dto.CanUseChat.Value;

        if (dto.CanUnmute.HasValue)
            participant.CanUnmute = dto.CanUnmute.Value;

        if (dto.CanTurnOnCamera.HasValue)
            participant.CanTurnOnCamera = dto.CanTurnOnCamera.Value;

        participant.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        _logger.LogInformation("Permissions updated for user {UserId} in meeting {MeetingId} by {RequestingUserId}", 
            participantUserId, meetingId, requestingUserId);

        return participant;
    }

    public async Task RemoveParticipantAsync(int meetingId, int participantUserId, int requestingUserId)
    {
        var requestingParticipant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == requestingUserId);

        if (requestingParticipant == null || 
            (requestingParticipant.Role != ParticipantRole.Host && requestingParticipant.Role != ParticipantRole.CoHost))
        {
            throw new UnauthorizedAccessException("Only hosts and co-hosts can remove participants");
        }

        var participant = await _context.MeetingParticipants
            .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == participantUserId);

        if (participant == null)
        {
            throw new ArgumentException("Participant not found in this meeting");
        }

        // Cannot remove the host
        if (participant.Role == ParticipantRole.Host)
        {
            throw new InvalidOperationException("Cannot remove the meeting host");
        }

        _context.MeetingParticipants.Remove(participant);
        await _context.SaveChangesAsync();

        _logger.LogInformation("User {UserId} removed from meeting {MeetingId} by {RequestingUserId}", 
            participantUserId, meetingId, requestingUserId);
    }

    public async Task<IEnumerable<MeetingParticipant>> GetMeetingParticipantsAsync(int meetingId, int requestingUserId)
    {
        if (!await CanUserAccessMeetingAsync(meetingId, requestingUserId))
        {
            throw new UnauthorizedAccessException("User does not have access to this meeting");
        }

        return await _context.MeetingParticipants
            .Where(p => p.MeetingId == meetingId)
            .Include(p => p.User)
            .OrderBy(p => p.Role)
            .ThenBy(p => p.JoinedAt)
            .ToListAsync();
    }

    public async Task<string> GenerateUniqueMeetingCodeAsync()
    {
        string code;
        bool exists;

        do
        {
            var random = new Random();
            code = $"{random.Next(100, 999)}-{random.Next(100, 999)}-{random.Next(100, 999)}";
            exists = await _context.Meetings.AnyAsync(m => m.MeetingCode == code);
        }
        while (exists);

        return code;
    }

    public async Task<bool> CanUserAccessMeetingAsync(int meetingId, int userId)
    {
        var meeting = await _context.Meetings
            .Include(m => m.Participants)
            .FirstOrDefaultAsync(m => m.Id == meetingId);

        if (meeting == null)
        {
            return false;
        }

        // Host always has access
        if (meeting.HostUserId == userId)
        {
            return true;
        }

        // Check if user is a participant
        return meeting.Participants.Any(p => p.UserId == userId);
    }
}
