@{
    ViewData["Title"] = "Dashboard";
    var user = ViewBag.User as GMCadiomMeeting.Web.Models.UserViewModel;
    var upcomingMeetings = ViewBag.UpcomingMeetings as IEnumerable<GMCadiomMeeting.Web.Models.MeetingViewModel>;
    var pendingInvitations = ViewBag.PendingInvitations as IEnumerable<GMCadiomMeeting.Web.Models.InvitationViewModel>;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-1">Welcome back, @(user?.DisplayName ?? "User")!</h1>
                    <p class="text-muted mb-0">Here's what's happening with your meetings today.</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-controller="Meeting" asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Meeting
                    </a>
                    <a asp-controller="Meeting" asp-action="Join" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>Join Meeting
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Stats -->
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-primary">@(upcomingMeetings?.Count() ?? 0)</div>
                            <div class="text-muted small">Upcoming Meetings</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-warning">@(pendingInvitations?.Count() ?? 0)</div>
                            <div class="text-muted small">Pending Invitations</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-success">0</div>
                            <div class="text-muted small">Active Meetings</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-bold text-info">0h</div>
                            <div class="text-muted small">Meeting Time Today</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upcoming Meetings -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt text-primary me-2"></i>Upcoming Meetings
                        </h5>
                        <a asp-controller="Meeting" asp-action="Index" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                </div>
                <div class="card-body">
                    @if (upcomingMeetings?.Any() == true)
                    {
                        @foreach (var meeting in upcomingMeetings)
                        {
                            <div class="d-flex align-items-center py-3 border-bottom">
                                <div class="flex-shrink-0">
                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-video text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-semibold">@meeting.Title</div>
                                    <div class="text-muted small">
                                        <i class="fas fa-clock me-1"></i>@meeting.ScheduledStartTime.ToString("MMM dd, yyyy h:mm tt")
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <a asp-controller="Meeting" asp-action="Details" asp-route-id="@meeting.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No upcoming meetings scheduled.</p>
                            <a asp-controller="Meeting" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Schedule Your First Meeting
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Pending Invitations -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-envelope text-warning me-2"></i>Invitations
                        </h5>
                        <a asp-controller="Meeting" asp-action="MyInvitations" class="btn btn-sm btn-outline-warning">View All</a>
                    </div>
                </div>
                <div class="card-body">
                    @if (pendingInvitations?.Any() == true)
                    {
                        @foreach (var invitation in pendingInvitations)
                        {
                            <div class="d-flex align-items-center py-2 border-bottom">
                                <div class="flex-grow-1">
                                    <div class="fw-semibold small">@invitation.MeetingTitle</div>
                                    <div class="text-muted small">From: @invitation.SentByUser</div>
                                </div>
                                <div class="flex-shrink-0">
                                    <div class="btn-group btn-group-sm">
                                        <form asp-controller="Meeting" asp-action="RespondToInvitation" method="post" class="d-inline">
                                            <input type="hidden" name="invitationId" value="@invitation.Id" />
                                            <input type="hidden" name="accept" value="true" />
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        <form asp-controller="Meeting" asp-action="RespondToInvitation" method="post" class="d-inline">
                                            <input type="hidden" name="invitationId" value="@invitation.Id" />
                                            <input type="hidden" name="accept" value="false" />
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <p class="text-muted small mb-0">No pending invitations.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom-0 py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-primary me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Meeting" asp-action="Create" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-plus fa-2x mb-2"></i>
                                <span>Create Meeting</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Meeting" asp-action="Join" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                                <span>Join Meeting</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Meeting" asp-action="Index" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>My Meetings</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Account" asp-action="Profile" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                <i class="fas fa-user fa-2x mb-2"></i>
                                <span>Profile</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
