@model GMCadiomMeeting.Web.Models.SendInvitationViewModel
@{
    ViewData["Title"] = "Send Invitations";
    var meeting = ViewBag.Meeting as GMCadiomMeeting.Web.Models.MeetingViewModel;
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-paper-plane me-2"></i>Send Meeting Invitations
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Meeting Info -->
                    @if (meeting != null)
                    {
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle fa-2x"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">@meeting.Title</h6>
                                    <div class="small">
                                        <i class="fas fa-calendar me-1"></i>@meeting.ScheduledStartTime.ToString("MMM dd, yyyy h:mm tt")
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-key me-1"></i>@meeting.MeetingCode
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <form asp-action="SendInvitation" method="post">
                        <input type="hidden" asp-for="MeetingId" />
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <!-- Recipients -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-users text-primary me-2"></i>Recipients
                            </h5>
                            
                            <div class="mb-3">
                                <label asp-for="EmailAddresses" class="form-label">Email Addresses <span class="text-danger">*</span></label>
                                <textarea asp-for="EmailAddresses" class="form-control" rows="4" 
                                         placeholder="Enter email addresses separated by commas or new lines&#10;<EMAIL>, <EMAIL>"></textarea>
                                <span asp-validation-for="EmailAddresses" class="text-danger small"></span>
                                <div class="form-text">
                                    Enter multiple email addresses separated by commas or new lines. 
                                    Maximum 50 recipients per invitation.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label asp-for="InvitedRole" class="form-label">Default Role</label>
                                <select asp-for="InvitedRole" class="form-select">
                                    <option value="@GMCadiomMeeting.Web.Models.ParticipantRole.Attendee">Attendee</option>
                                    <option value="@GMCadiomMeeting.Web.Models.ParticipantRole.Presenter">Presenter</option>
                                    <option value="@GMCadiomMeeting.Web.Models.ParticipantRole.CoHost">Co-Host</option>
                                </select>
                                <span asp-validation-for="InvitedRole" class="text-danger small"></span>
                                <div class="form-text">The role that will be assigned to invited participants</div>
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-comment text-primary me-2"></i>Personal Message
                            </h5>
                            
                            <div class="mb-3">
                                <label asp-for="PersonalMessage" class="form-label">Custom Message</label>
                                <textarea asp-for="PersonalMessage" class="form-control" rows="4" 
                                         placeholder="Add a personal message to your invitation (optional)"></textarea>
                                <span asp-validation-for="PersonalMessage" class="text-danger small"></span>
                                <div class="form-text">This message will be included in the invitation email</div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-cog text-primary me-2"></i>Invitation Settings
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label asp-for="ExpiresAt" class="form-label">Expiration Date</label>
                                    <input asp-for="ExpiresAt" type="datetime-local" class="form-control" />
                                    <span asp-validation-for="ExpiresAt" class="text-danger small"></span>
                                    <div class="form-text">When this invitation should expire (optional)</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Reminder Settings</label>
                                    <div class="form-check">
                                        <input asp-for="SendReminder" class="form-check-input" />
                                        <label asp-for="SendReminder" class="form-check-label">
                                            Send reminder before meeting
                                        </label>
                                    </div>
                                    <div class="form-text">Send an email reminder 1 hour before the meeting</div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview -->
                        <div class="mb-4">
                            <h5 class="border-bottom pb-2 mb-3">
                                <i class="fas fa-eye text-primary me-2"></i>Invitation Preview
                            </h5>
                            
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">You're invited to join a meeting</h6>
                                    <p class="card-text">
                                        <strong>@(meeting?.Title ?? "Meeting Title")</strong><br>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>@(meeting?.ScheduledStartTime.ToString("MMM dd, yyyy h:mm tt") ?? "Date & Time")<br>
                                            <i class="fas fa-user me-1"></i>Hosted by @(meeting?.HostUser?.DisplayName ?? "Host Name")
                                        </small>
                                    </p>
                                    
                                    <div id="personalMessagePreview" class="alert alert-secondary" style="display: none;">
                                        <small><strong>Personal Message:</strong></small>
                                        <div id="personalMessageContent"></div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-primary">Meeting Code: @(meeting?.MeetingCode ?? "XXX-XXX-XXX")</span>
                                        <span class="badge bg-secondary">Role: <span id="rolePreview">Participant</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Invitations" asp-route-id="@Model.MeetingId" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Invitations
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Send Invitations
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>Tips for Sending Invitations
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Double-check email addresses for accuracy
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Include a clear personal message
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Set appropriate roles for participants
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Consider setting expiration dates for security
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const personalMessageInput = document.querySelector('textarea[name="PersonalMessage"]');
            const roleSelect = document.querySelector('select[name="InvitedRole"]');
            const personalMessagePreview = document.getElementById('personalMessagePreview');
            const personalMessageContent = document.getElementById('personalMessageContent');
            const rolePreview = document.getElementById('rolePreview');

            // Update personal message preview
            personalMessageInput.addEventListener('input', function() {
                if (this.value.trim()) {
                    personalMessageContent.textContent = this.value;
                    personalMessagePreview.style.display = 'block';
                } else {
                    personalMessagePreview.style.display = 'none';
                }
            });

            // Update role preview
            roleSelect.addEventListener('change', function() {
                rolePreview.textContent = this.options[this.selectedIndex].text;
            });

            // Set default expiration time (1 hour before meeting)
            const expiresInput = document.querySelector('input[name="ExpiresAt"]');
            if (!expiresInput.value && '@(meeting?.ScheduledStartTime)') {
                const meetingTime = new Date('@(meeting?.ScheduledStartTime.ToString("yyyy-MM-ddTHH:mm:ss"))');
                const expirationTime = new Date(meetingTime.getTime() - 60 * 60 * 1000); // 1 hour before
                expiresInput.value = expirationTime.toISOString().slice(0, 16);
            }

            // Email validation
            const emailTextarea = document.querySelector('textarea[name="EmailAddresses"]');
            emailTextarea.addEventListener('blur', function() {
                const emails = this.value.split(',').concat(this.value.split('\n')).map(function(email) { return email.trim(); }).filter(function(email) { return email; });
                const invalidEmails = emails.filter(function(email) { return !validateEmail(email); });
                
                if (invalidEmails.length > 0) {
                    showToast(`Invalid email addresses: ${invalidEmails.join(', ')}`, 'warning');
                }
                
                if (emails.length > 50) {
                    showToast('Maximum 50 recipients allowed per invitation', 'warning');
                }
            });
        });

        function validateEmail(email) {
            const re = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
            return re.test(email);
        }
    </script>
}
