@model GMCadiomMeeting.Web.Models.CreateMeetingViewModel
@{
    ViewData["Title"] = "Create Meeting";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Create New Meeting
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-12 mb-4">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-info-circle text-primary me-2"></i>Basic Information
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Title" class="form-label">Meeting Title <span class="text-danger">*</span></label>
                                <input asp-for="Title" class="form-control" placeholder="Enter meeting title" />
                                <span asp-validation-for="Title" class="text-danger small"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="Type" class="form-label">Meeting Type</label>
                                <select asp-for="Type" class="form-select">
                                    <option value="0">One-on-One</option>
                                    <option value="1" selected>Group Meeting</option>
                                    <option value="2">Webinar</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">Description</label>
                            <textarea asp-for="Description" class="form-control" rows="3" placeholder="Optional meeting description or agenda"></textarea>
                            <span asp-validation-for="Description" class="text-danger small"></span>
                        </div>

                        <!-- Schedule -->
                        <div class="row">
                            <div class="col-12 mb-4">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>Schedule
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="ScheduledStartTime" class="form-label">Start Time <span class="text-danger">*</span></label>
                                <input asp-for="ScheduledStartTime" type="datetime-local" class="form-control" />
                                <span asp-validation-for="ScheduledStartTime" class="text-danger small"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ScheduledEndTime" class="form-label">End Time <span class="text-danger">*</span></label>
                                <input asp-for="ScheduledEndTime" type="datetime-local" class="form-control" />
                                <span asp-validation-for="ScheduledEndTime" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input asp-for="IsRecurring" class="form-check-input" />
                                    <label asp-for="IsRecurring" class="form-check-label">
                                        Recurring Meeting
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3" id="recurrencePattern" style="display: none;">
                                <label asp-for="RecurrencePattern" class="form-label">Recurrence Pattern</label>
                                <select asp-for="RecurrencePattern" class="form-select">
                                    <option value="">Select pattern</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                                <span asp-validation-for="RecurrencePattern" class="text-danger small"></span>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="row">
                            <div class="col-12 mb-4">
                                <h5 class="border-bottom pb-2 mb-3">
                                    <i class="fas fa-cog text-primary me-2"></i>Meeting Settings
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="MaxParticipants" class="form-label">Maximum Participants</label>
                                <input asp-for="MaxParticipants" type="number" class="form-control" min="2" max="1000" />
                                <span asp-validation-for="MaxParticipants" class="text-danger small"></span>
                                <div class="form-text">Maximum number of participants allowed (2-1000)</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label">Meeting Password</label>
                                <input asp-for="Password" class="form-control" placeholder="Optional password" />
                                <span asp-validation-for="Password" class="text-danger small"></span>
                                <div class="form-text">Leave blank for no password protection</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input asp-for="IsRecordingEnabled" class="form-check-input" />
                                    <label asp-for="IsRecordingEnabled" class="form-check-label">
                                        <i class="fas fa-record-vinyl me-1"></i>Enable Recording
                                    </label>
                                </div>
                                <div class="form-text">Allow meeting to be recorded</div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <a asp-controller="Home" asp-action="Dashboard" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-plus me-2"></i>Create Meeting
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb text-warning me-2"></i>Quick Tips
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Choose a descriptive title for easy identification
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Set realistic time estimates for better planning
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Use passwords for sensitive meetings
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Enable recording for important discussions
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const isRecurringCheckbox = document.querySelector('input[name="IsRecurring"]');
            const recurrencePatternDiv = document.getElementById('recurrencePattern');

            // Toggle recurrence pattern visibility
            isRecurringCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    recurrencePatternDiv.style.display = 'block';
                } else {
                    recurrencePatternDiv.style.display = 'none';
                    document.querySelector('select[name="RecurrencePattern"]').value = '';
                }
            });

            // Set default times
            const now = new Date();
            const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
            const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // 1 hour duration

            const startInput = document.querySelector('input[name="ScheduledStartTime"]');
            const endInput = document.querySelector('input[name="ScheduledEndTime"]');

            if (!startInput.value) {
                startInput.value = startTime.toISOString().slice(0, 16);
            }
            if (!endInput.value) {
                endInput.value = endTime.toISOString().slice(0, 16);
            }

            // Auto-update end time when start time changes
            startInput.addEventListener('change', function() {
                const newStartTime = new Date(this.value);
                const newEndTime = new Date(newStartTime.getTime() + 60 * 60 * 1000);
                endInput.value = newEndTime.toISOString().slice(0, 16);
            });
        });
    </script>
}
