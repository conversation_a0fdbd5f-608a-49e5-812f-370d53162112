@model GMCadiomMeeting.Web.Models.MeetingViewModel
@{
    ViewData["Title"] = $"Meeting: {Model.Title}";
    Layout = null; // Full-screen layout for meeting room
    var user = ViewBag.User as GMCadiomMeeting.Web.Models.UserViewModel;
    var participant = ViewBag.Participant as GMCadiomMeeting.Web.Models.MeetingParticipantViewModel;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        .meeting-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .video-grid {
            flex: 1;
            display: grid;
            gap: 10px;
            padding: 10px;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            grid-auto-rows: minmax(200px, 1fr);
        }
        .video-tile {
            background: #2a2a2a;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        .video-tile video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .video-tile .participant-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .video-tile .participant-status {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .controls-bar {
            background: #2a2a2a;
            padding: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .control-btn.active {
            background: #28a745;
            color: white;
        }
        .control-btn.inactive {
            background: #dc3545;
            color: white;
        }
        .control-btn.neutral {
            background: #6c757d;
            color: white;
        }
        .control-btn:hover {
            transform: scale(1.1);
        }
        .chat-panel {
            position: fixed;
            right: -400px;
            top: 0;
            width: 400px;
            height: 100vh;
            background: #2a2a2a;
            transition: right 0.3s;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }
        .chat-panel.open {
            right: 0;
        }
        .chat-header {
            padding: 15px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        .chat-input {
            padding: 15px;
            border-top: 1px solid #444;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            background: #3a3a3a;
            border-radius: 8px;
        }
        .message-sender {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .participants-panel {
            position: fixed;
            right: -300px;
            top: 0;
            width: 300px;
            height: 100vh;
            background: #2a2a2a;
            transition: right 0.3s;
            z-index: 1000;
        }
        .participants-panel.open {
            right: 0;
        }
        .meeting-info {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0,0,0,0.7);
            padding: 10px 15px;
            border-radius: 8px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="meeting-container">
        <!-- Meeting Info -->
        <div class="meeting-info">
            <div class="fw-bold">@Model.Title</div>
            <div class="small text-muted">Meeting ID: @Model.MeetingCode</div>
        </div>

        <!-- Video Grid -->
        <div class="video-grid" id="videoGrid">
            <!-- Local Video -->
            <div class="video-tile">
                <video id="localVideo" autoplay muted></video>
                <div class="participant-info">
                    @(user?.DisplayName ?? "You") (You)
                </div>
                <div class="participant-status">
                    <div class="status-icon bg-success" id="localCameraStatus">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="status-icon bg-success" id="localMicStatus">
                        <i class="fas fa-microphone"></i>
                    </div>
                </div>
            </div>
            
            <!-- Remote videos will be added dynamically -->
        </div>

        <!-- Controls Bar -->
        <div class="controls-bar">
            <button class="control-btn active" id="micBtn" title="Microphone">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn active" id="cameraBtn" title="Camera">
                <i class="fas fa-video"></i>
            </button>
            <button class="control-btn neutral" id="shareBtn" title="Share Screen">
                <i class="fas fa-desktop"></i>
            </button>
            <button class="control-btn neutral" id="chatBtn" title="Chat">
                <i class="fas fa-comments"></i>
            </button>
            <button class="control-btn neutral" id="participantsBtn" title="Participants">
                <i class="fas fa-users"></i>
            </button>
            <button class="control-btn neutral" id="settingsBtn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="control-btn inactive" id="leaveBtn" title="Leave Meeting">
                <i class="fas fa-phone-slash"></i>
            </button>
        </div>
    </div>

    <!-- Chat Panel -->
    <div class="chat-panel" id="chatPanel">
        <div class="chat-header">
            <h6 class="mb-0">Meeting Chat</h6>
            <button class="btn btn-sm btn-outline-light" onclick="toggleChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will be added dynamically -->
        </div>
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="form-control" id="messageInput" placeholder="Type a message...">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Participants Panel -->
    <div class="participants-panel" id="participantsPanel">
        <div class="chat-header">
            <h6 class="mb-0">Participants (@Model.Participants.Count)</h6>
            <button class="btn btn-sm btn-outline-light" onclick="toggleParticipants()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-3" id="participantsList">
            @foreach (var p in Model.Participants)
            {
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-semibold">@(p.DisplayNameInMeeting ?? p.User?.DisplayName)</div>
                        <div class="small text-muted">@p.Role</div>
                    </div>
                    <div class="flex-shrink-0">
                        @if (p.IsCameraEnabled)
                        {
                            <i class="fas fa-video text-success me-1"></i>
                        }
                        @if (p.IsMicrophoneEnabled)
                        {
                            <i class="fas fa-microphone text-success"></i>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    
    <script>
        // Meeting room functionality
        let localStream = null;
        let connection = null;
        let isCameraOn = @(participant?.IsCameraEnabled.ToString().ToLower() ?? "false");
        let isMicOn = @(participant?.IsMicrophoneEnabled.ToString().ToLower() ?? "false");
        let isScreenSharing = false;

        const meetingId = @Model.Id;
        const userId = @(user?.Id ?? 0);
        const userName = '@(user?.DisplayName ?? "Guest")';

        // Initialize meeting room
        document.addEventListener('DOMContentLoaded', async function() {
            await initializeMedia();
            await initializeSignalR();
            setupControlHandlers();
        });

        async function initializeMedia() {
            try {
                localStream = await navigator.mediaDevices.getUserMedia({ 
                    video: isCameraOn, 
                    audio: isMicOn 
                });
                
                const localVideo = document.getElementById('localVideo');
                localVideo.srcObject = localStream;
                
                updateControlStates();
            } catch (error) {
                console.error('Error accessing media devices:', error);
            }
        }

        async function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl('/meetingHub')
                .build();

            // Event handlers
            connection.on('ParticipantJoined', function(participant) {
                console.log('Participant joined:', participant);
                addParticipantToUI(participant);
            });

            connection.on('ParticipantLeft', function(participant) {
                console.log('Participant left:', participant);
                removeParticipantFromUI(participant);
            });

            connection.on('ChatMessage', function(message) {
                addChatMessage(message);
            });

            connection.on('ParticipantMediaStateChanged', function(data) {
                updateParticipantMediaState(data);
            });

            try {
                await connection.start();
                await connection.invoke('JoinMeeting', meetingId, userId, userName);
                console.log('Connected to meeting hub');
            } catch (error) {
                console.error('Error connecting to meeting hub:', error);
            }
        }

        function setupControlHandlers() {
            // Microphone toggle
            document.getElementById('micBtn').addEventListener('click', toggleMicrophone);
            
            // Camera toggle
            document.getElementById('cameraBtn').addEventListener('click', toggleCamera);
            
            // Screen share
            document.getElementById('shareBtn').addEventListener('click', toggleScreenShare);
            
            // Chat toggle
            document.getElementById('chatBtn').addEventListener('click', toggleChat);
            
            // Participants toggle
            document.getElementById('participantsBtn').addEventListener('click', toggleParticipants);
            
            // Leave meeting
            document.getElementById('leaveBtn').addEventListener('click', leaveMeeting);

            // Chat input
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        async function toggleMicrophone() {
            isMicOn = !isMicOn;
            
            if (localStream) {
                localStream.getAudioTracks().forEach(track => {
                    track.enabled = isMicOn;
                });
            }
            
            updateControlStates();
            
            if (connection) {
                await connection.invoke('UpdateMediaState', meetingId, isCameraOn, isMicOn);
            }
        }

        async function toggleCamera() {
            isCameraOn = !isCameraOn;
            
            if (localStream) {
                localStream.getVideoTracks().forEach(track => {
                    track.enabled = isCameraOn;
                });
            }
            
            updateControlStates();
            
            if (connection) {
                await connection.invoke('UpdateMediaState', meetingId, isCameraOn, isMicOn);
            }
        }

        async function toggleScreenShare() {
            if (!isScreenSharing) {
                try {
                    const screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true });
                    // Replace video track with screen share
                    const videoTrack = screenStream.getVideoTracks()[0];
                    
                    if (localStream) {
                        const sender = localStream.getVideoTracks()[0];
                        // In a real implementation, you'd replace the track in peer connections
                    }
                    
                    isScreenSharing = true;
                    document.getElementById('shareBtn').classList.add('active');
                    
                    if (connection) {
                        await connection.invoke('StartScreenSharing', meetingId, 'screen-' + Date.now(), 'fullscreen');
                    }
                    
                    videoTrack.onended = () => {
                        stopScreenShare();
                    };
                } catch (error) {
                    console.error('Error starting screen share:', error);
                }
            } else {
                stopScreenShare();
            }
        }

        async function stopScreenShare() {
            isScreenSharing = false;
            document.getElementById('shareBtn').classList.remove('active');
            
            if (connection) {
                await connection.invoke('StopScreenSharing', meetingId, 'screen-' + Date.now());
            }
        }

        function toggleChat() {
            const chatPanel = document.getElementById('chatPanel');
            chatPanel.classList.toggle('open');
        }

        function toggleParticipants() {
            const participantsPanel = document.getElementById('participantsPanel');
            participantsPanel.classList.toggle('open');
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message && connection) {
                await connection.invoke('SendChatMessage', meetingId, message, 'text');
                input.value = '';
            }
        }

        function addChatMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <div class="message-sender">${message.SenderName}</div>
                <div>${message.Content}</div>
                <div class="small text-muted">${new Date(message.SentAt).toLocaleTimeString()}</div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function updateControlStates() {
            const micBtn = document.getElementById('micBtn');
            const cameraBtn = document.getElementById('cameraBtn');
            const localMicStatus = document.getElementById('localMicStatus');
            const localCameraStatus = document.getElementById('localCameraStatus');
            
            if (isMicOn) {
                micBtn.classList.remove('inactive');
                micBtn.classList.add('active');
                localMicStatus.innerHTML = '<i class="fas fa-microphone"></i>';
            } else {
                micBtn.classList.remove('active');
                micBtn.classList.add('inactive');
                localMicStatus.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            }
            
            if (isCameraOn) {
                cameraBtn.classList.remove('inactive');
                cameraBtn.classList.add('active');
                localCameraStatus.innerHTML = '<i class="fas fa-video"></i>';
            } else {
                cameraBtn.classList.remove('active');
                cameraBtn.classList.add('inactive');
                localCameraStatus.innerHTML = '<i class="fas fa-video-slash"></i>';
            }
        }

        async function leaveMeeting() {
            if (confirm('Are you sure you want to leave the meeting?')) {
                if (connection) {
                    await connection.invoke('LeaveMeeting');
                    await connection.stop();
                }
                
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                }
                
                window.location.href = '@Url.Action("Dashboard", "Home")';
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
            }
            if (connection) {
                connection.stop();
            }
        });
    </script>
</body>
</html>
