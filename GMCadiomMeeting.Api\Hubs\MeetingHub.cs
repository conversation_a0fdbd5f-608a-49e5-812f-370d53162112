using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;
using System.Collections.Concurrent;

namespace GMCadiomMeeting.Api.Hubs;

/// <summary>
/// SignalR hub for real-time meeting communication
/// Handles video calling, chat, screen sharing, and participant management
/// </summary>
public class MeetingHub : Hub
{
    private readonly GMCadiomMeetingDbContext _context;
    private readonly ILogger<MeetingHub> _logger;
    
    // Static dictionaries to track connections and meetings
    private static readonly ConcurrentDictionary<string, UserConnection> _connections = new();
    private static readonly ConcurrentDictionary<int, HashSet<string>> _meetingConnections = new();

    public MeetingHub(GMCadiomMeetingDbContext context, ILogger<MeetingHub> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Join a meeting room
    /// </summary>
    public async Task JoinMeeting(int meetingId, int userId, string displayName)
    {
        try
        {
            // Verify user is a participant
            var participant = await _context.MeetingParticipants
                .Include(p => p.User)
                .Include(p => p.Meeting)
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userId);

            if (participant == null)
            {
                await Clients.Caller.SendAsync("Error", "You are not a participant in this meeting");
                return;
            }

            // Add connection to tracking
            var userConnection = new UserConnection
            {
                UserId = userId,
                MeetingId = meetingId,
                DisplayName = displayName,
                ConnectionId = Context.ConnectionId
            };

            _connections[Context.ConnectionId] = userConnection;

            // Add to meeting group
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Meeting_{meetingId}");

            // Track meeting connections
            _meetingConnections.AddOrUpdate(meetingId, 
                new HashSet<string> { Context.ConnectionId },
                (key, existing) => { existing.Add(Context.ConnectionId); return existing; });

            // Update participant status
            participant.Status = ConnectionStatus.Connected;
            participant.JoinedAt = DateTime.UtcNow;
            participant.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            // Notify other participants
            await Clients.Group($"Meeting_{meetingId}")
                .SendAsync("ParticipantJoined", new
                {
                    UserId = userId,
                    DisplayName = displayName,
                    Role = participant.Role.ToString(),
                    JoinedAt = participant.JoinedAt
                });

            // Send current participants list to the new joiner
            var currentParticipants = await GetMeetingParticipants(meetingId);
            await Clients.Caller.SendAsync("ParticipantsList", currentParticipants);

            _logger.LogInformation("User {UserId} joined meeting {MeetingId} via SignalR", userId, meetingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting {MeetingId} for user {UserId}", meetingId, userId);
            await Clients.Caller.SendAsync("Error", "Failed to join meeting");
        }
    }

    /// <summary>
    /// Leave a meeting room
    /// </summary>
    public async Task LeaveMeeting()
    {
        if (_connections.TryGetValue(Context.ConnectionId, out var userConnection))
        {
            await HandleUserDisconnection(userConnection);
        }
    }

    /// <summary>
    /// Send a chat message
    /// </summary>
    public async Task SendChatMessage(int meetingId, string message, string messageType = "text", int? recipientId = null)
    {
        try
        {
            if (!_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                await Clients.Caller.SendAsync("Error", "Connection not found");
                return;
            }

            // Verify user can send messages
            var participant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && 
                                         p.UserId == userConnection.UserId && 
                                         p.CanUseChat);

            if (participant == null)
            {
                await Clients.Caller.SendAsync("Error", "You are not authorized to send messages");
                return;
            }

            // Create chat message
            var chatMessage = new ChatMessage
            {
                MeetingId = meetingId,
                SenderId = userConnection.UserId,
                RecipientId = recipientId,
                Content = message,
                Type = Enum.Parse<MessageType>(messageType, true),
                Scope = recipientId.HasValue ? MessageScope.Private : MessageScope.Public,
                SentAt = DateTime.UtcNow
            };

            _context.ChatMessages.Add(chatMessage);
            await _context.SaveChangesAsync();

            // Send to appropriate recipients
            var messageData = new
            {
                Id = chatMessage.Id,
                SenderId = userConnection.UserId,
                SenderName = userConnection.DisplayName,
                RecipientId = recipientId,
                Content = message,
                Type = messageType,
                Scope = chatMessage.Scope.ToString(),
                SentAt = chatMessage.SentAt
            };

            if (recipientId.HasValue)
            {
                // Private message - send to sender and recipient only
                var recipientConnection = _connections.Values
                    .FirstOrDefault(c => c.UserId == recipientId.Value && c.MeetingId == meetingId);

                if (recipientConnection != null)
                {
                    await Clients.Client(recipientConnection.ConnectionId).SendAsync("ChatMessage", messageData);
                }
                await Clients.Caller.SendAsync("ChatMessage", messageData);
            }
            else
            {
                // Public message - send to all meeting participants
                await Clients.Group($"Meeting_{meetingId}").SendAsync("ChatMessage", messageData);
            }

            _logger.LogInformation("Chat message sent in meeting {MeetingId} by user {UserId}", meetingId, userConnection.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat message in meeting {MeetingId}", meetingId);
            await Clients.Caller.SendAsync("Error", "Failed to send message");
        }
    }

    /// <summary>
    /// Update participant media state (camera/microphone)
    /// </summary>
    public async Task UpdateMediaState(int meetingId, bool isCameraEnabled, bool isMicrophoneEnabled)
    {
        try
        {
            if (!_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                return;
            }

            var participant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userConnection.UserId);

            if (participant != null)
            {
                participant.IsCameraEnabled = isCameraEnabled;
                participant.IsMicrophoneEnabled = isMicrophoneEnabled;
                participant.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                // Notify other participants
                await Clients.Group($"Meeting_{meetingId}")
                    .SendAsync("ParticipantMediaStateChanged", new
                    {
                        UserId = userConnection.UserId,
                        IsCameraEnabled = isCameraEnabled,
                        IsMicrophoneEnabled = isMicrophoneEnabled
                    });
            }
        }
        catch (Exception ex)
        {
            if (_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                _logger.LogError(ex, "Error updating media state for user {UserId} in meeting {MeetingId}",
                    userConnection.UserId, meetingId);
            }
            else
            {
                _logger.LogError(ex, "Error updating media state in meeting {MeetingId}", meetingId);
            }
        }
    }

    /// <summary>
    /// Start screen sharing
    /// </summary>
    public async Task StartScreenSharing(int meetingId, string sessionId, string shareType = "fullscreen")
    {
        try
        {
            if (!_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                return;
            }

            var participant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && 
                                         p.UserId == userConnection.UserId && 
                                         p.CanShareScreen);

            if (participant == null)
            {
                await Clients.Caller.SendAsync("Error", "You are not authorized to share screen");
                return;
            }

            // Create screen sharing metadata
            var screenSharing = new ScreenSharingMetadata
            {
                MeetingId = meetingId,
                SharingUserId = userConnection.UserId,
                SessionId = sessionId,
                Type = Enum.Parse<ScreenSharingType>(shareType, true),
                Status = ScreenSharingStatus.Active,
                StartedAt = DateTime.UtcNow
            };

            _context.ScreenSharingMetadata.Add(screenSharing);

            // Update participant status
            participant.IsScreenSharing = true;
            participant.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Notify other participants
            await Clients.Group($"Meeting_{meetingId}")
                .SendAsync("ScreenSharingStarted", new
                {
                    UserId = userConnection.UserId,
                    UserName = userConnection.DisplayName,
                    SessionId = sessionId,
                    Type = shareType
                });

            _logger.LogInformation("Screen sharing started by user {UserId} in meeting {MeetingId}", 
                userConnection.UserId, meetingId);
        }
        catch (Exception ex)
        {
            if (_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                _logger.LogError(ex, "Error starting screen sharing for user {UserId} in meeting {MeetingId}",
                    userConnection.UserId, meetingId);
            }
            else
            {
                _logger.LogError(ex, "Error starting screen sharing in meeting {MeetingId}", meetingId);
            }
            await Clients.Caller.SendAsync("Error", "Failed to start screen sharing");
        }
    }

    /// <summary>
    /// Stop screen sharing
    /// </summary>
    public async Task StopScreenSharing(int meetingId, string sessionId)
    {
        try
        {
            if (!_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                return;
            }

            var screenSharing = await _context.ScreenSharingMetadata
                .FirstOrDefaultAsync(s => s.SessionId == sessionId && 
                                         s.MeetingId == meetingId && 
                                         s.SharingUserId == userConnection.UserId);

            if (screenSharing != null)
            {
                screenSharing.Status = ScreenSharingStatus.Stopped;
                screenSharing.EndedAt = DateTime.UtcNow;
                screenSharing.DurationSeconds = (int)(DateTime.UtcNow - screenSharing.StartedAt).TotalSeconds;
                screenSharing.UpdatedAt = DateTime.UtcNow;

                var participant = await _context.MeetingParticipants
                    .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userConnection.UserId);

                if (participant != null)
                {
                    participant.IsScreenSharing = false;
                    participant.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                // Notify other participants
                await Clients.Group($"Meeting_{meetingId}")
                    .SendAsync("ScreenSharingStopped", new
                    {
                        UserId = userConnection.UserId,
                        SessionId = sessionId
                    });

                _logger.LogInformation("Screen sharing stopped by user {UserId} in meeting {MeetingId}", 
                    userConnection.UserId, meetingId);
            }
        }
        catch (Exception ex)
        {
            if (_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                _logger.LogError(ex, "Error stopping screen sharing for user {UserId} in meeting {MeetingId}",
                    userConnection.UserId, meetingId);
            }
            else
            {
                _logger.LogError(ex, "Error stopping screen sharing in meeting {MeetingId}", meetingId);
            }
        }
    }

    /// <summary>
    /// Handle WebRTC signaling for video calls
    /// </summary>
    public async Task SendSignal(int meetingId, int targetUserId, object signal, string signalType)
    {
        try
        {
            if (!_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                return;
            }

            var targetConnection = _connections.Values
                .FirstOrDefault(c => c.UserId == targetUserId && c.MeetingId == meetingId);

            if (targetConnection != null)
            {
                await Clients.Client(targetConnection.ConnectionId)
                    .SendAsync("ReceiveSignal", new
                    {
                        FromUserId = userConnection.UserId,
                        FromUserName = userConnection.DisplayName,
                        Signal = signal,
                        SignalType = signalType
                    });
            }
        }
        catch (Exception ex)
        {
            if (_connections.TryGetValue(Context.ConnectionId, out var userConnection))
            {
                _logger.LogError(ex, "Error sending WebRTC signal from user {UserId} to user {TargetUserId}",
                    userConnection.UserId, targetUserId);
            }
            else
            {
                _logger.LogError(ex, "Error sending WebRTC signal to user {TargetUserId}", targetUserId);
            }
        }
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        if (_connections.TryRemove(Context.ConnectionId, out var userConnection))
        {
            await HandleUserDisconnection(userConnection);
        }

        await base.OnDisconnectedAsync(exception);
    }

    private async Task HandleUserDisconnection(UserConnection userConnection)
    {
        try
        {
            // Remove from meeting group
            await Groups.RemoveFromGroupAsync(userConnection.ConnectionId, $"Meeting_{userConnection.MeetingId}");

            // Update meeting connections tracking
            if (_meetingConnections.TryGetValue(userConnection.MeetingId, out var connections))
            {
                connections.Remove(userConnection.ConnectionId);
                if (connections.Count == 0)
                {
                    _meetingConnections.TryRemove(userConnection.MeetingId, out _);
                }
            }

            // Update participant status in database
            var participant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == userConnection.MeetingId && 
                                         p.UserId == userConnection.UserId);

            if (participant != null)
            {
                participant.Status = ConnectionStatus.Disconnected;
                participant.LeftAt = DateTime.UtcNow;
                participant.UpdatedAt = DateTime.UtcNow;

                if (participant.JoinedAt.HasValue)
                {
                    participant.DurationSeconds = (int)(DateTime.UtcNow - participant.JoinedAt.Value).TotalSeconds;
                }

                await _context.SaveChangesAsync();
            }

            // Notify other participants
            await Clients.Group($"Meeting_{userConnection.MeetingId}")
                .SendAsync("ParticipantLeft", new
                {
                    UserId = userConnection.UserId,
                    DisplayName = userConnection.DisplayName,
                    LeftAt = DateTime.UtcNow
                });

            _logger.LogInformation("User {UserId} disconnected from meeting {MeetingId}", 
                userConnection.UserId, userConnection.MeetingId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling disconnection for user {UserId} in meeting {MeetingId}", 
                userConnection.UserId, userConnection.MeetingId);
        }
    }

    private async Task<object[]> GetMeetingParticipants(int meetingId)
    {
        var participants = await _context.MeetingParticipants
            .Where(p => p.MeetingId == meetingId)
            .Include(p => p.User)
            .Select(p => new
            {
                UserId = p.UserId,
                DisplayName = p.DisplayNameInMeeting ?? p.User.DisplayName,
                Role = p.Role.ToString(),
                Status = p.Status.ToString(),
                IsCameraEnabled = p.IsCameraEnabled,
                IsMicrophoneEnabled = p.IsMicrophoneEnabled,
                IsScreenSharing = p.IsScreenSharing,
                JoinedAt = p.JoinedAt
            })
            .ToArrayAsync();

        return participants.Cast<object>().ToArray();
    }
}

/// <summary>
/// Represents a user connection in a meeting
/// </summary>
public class UserConnection
{
    public int UserId { get; set; }
    public int MeetingId { get; set; }
    public string DisplayName { get; set; } = string.Empty;
    public string ConnectionId { get; set; } = string.Empty;
}
