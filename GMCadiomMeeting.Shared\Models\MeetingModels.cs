using System.ComponentModel.DataAnnotations;
using GMCadiomMeeting.Shared.Enums;

namespace GMCadiomMeeting.Shared.Models;

/// <summary>
/// Meeting information for API responses
/// </summary>
public class MeetingDto
{
    /// <summary>
    /// Meeting unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Meeting type
    /// </summary>
    public MeetingType Type { get; set; }

    /// <summary>
    /// Meeting status
    /// </summary>
    public MeetingStatus Status { get; set; }

    /// <summary>
    /// Unique meeting code for joining
    /// </summary>
    public string MeetingCode { get; set; } = string.Empty;

    /// <summary>
    /// Meeting password (if required)
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    public int MaxParticipants { get; set; }

    /// <summary>
    /// Scheduled start time
    /// </summary>
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Actual start time (when meeting was started)
    /// </summary>
    public DateTime? ActualStartTime { get; set; }

    /// <summary>
    /// Actual end time (when meeting was ended)
    /// </summary>
    public DateTime? ActualEndTime { get; set; }

    /// <summary>
    /// Indicates if recording is enabled
    /// </summary>
    public bool IsRecordingEnabled { get; set; }

    /// <summary>
    /// URL to meeting recording (if available)
    /// </summary>
    public string? RecordingUrl { get; set; }

    /// <summary>
    /// Indicates if the meeting is recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurrence pattern (daily, weekly, monthly, etc.)
    /// </summary>
    public string? RecurrencePattern { get; set; }

    /// <summary>
    /// Meeting invitation link
    /// </summary>
    public string? InvitationLink { get; set; }

    /// <summary>
    /// Host user ID
    /// </summary>
    public int HostUserId { get; set; }

    /// <summary>
    /// Host user information
    /// </summary>
    public UserDto? HostUser { get; set; }

    /// <summary>
    /// List of meeting participants
    /// </summary>
    public List<MeetingParticipantDto> Participants { get; set; } = new();

    /// <summary>
    /// Date when the meeting was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Date when the meeting was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Request model for creating a new meeting
/// </summary>
public class CreateMeetingRequest
{
    /// <summary>
    /// Meeting title
    /// </summary>
    [Required(ErrorMessage = "Meeting title is required")]
    [StringLength(200, MinimumLength = 3, ErrorMessage = "Title must be between 3 and 200 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Meeting type
    /// </summary>
    [Required(ErrorMessage = "Meeting type is required")]
    public MeetingType Type { get; set; }

    /// <summary>
    /// Meeting password (optional)
    /// </summary>
    [StringLength(50, ErrorMessage = "Password cannot exceed 50 characters")]
    public string? Password { get; set; }

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    [Range(2, 1000, ErrorMessage = "Maximum participants must be between 2 and 1000")]
    public int MaxParticipants { get; set; } = 100;

    /// <summary>
    /// Scheduled start time
    /// </summary>
    [Required(ErrorMessage = "Scheduled start time is required")]
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    [Required(ErrorMessage = "Scheduled end time is required")]
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Indicates if recording is enabled
    /// </summary>
    public bool IsRecordingEnabled { get; set; }

    /// <summary>
    /// Indicates if the meeting is recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurrence pattern (if recurring)
    /// </summary>
    [StringLength(100, ErrorMessage = "Recurrence pattern cannot exceed 100 characters")]
    public string? RecurrencePattern { get; set; }
}

/// <summary>
/// Request model for updating a meeting
/// </summary>
public class UpdateMeetingRequest
{
    /// <summary>
    /// Meeting title
    /// </summary>
    [Required(ErrorMessage = "Meeting title is required")]
    [StringLength(200, MinimumLength = 3, ErrorMessage = "Title must be between 3 and 200 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Meeting description
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Meeting password (optional)
    /// </summary>
    [StringLength(50, ErrorMessage = "Password cannot exceed 50 characters")]
    public string? Password { get; set; }

    /// <summary>
    /// Maximum number of participants allowed
    /// </summary>
    [Range(2, 1000, ErrorMessage = "Maximum participants must be between 2 and 1000")]
    public int MaxParticipants { get; set; }

    /// <summary>
    /// Scheduled start time
    /// </summary>
    [Required(ErrorMessage = "Scheduled start time is required")]
    public DateTime ScheduledStartTime { get; set; }

    /// <summary>
    /// Scheduled end time
    /// </summary>
    [Required(ErrorMessage = "Scheduled end time is required")]
    public DateTime ScheduledEndTime { get; set; }

    /// <summary>
    /// Indicates if recording is enabled
    /// </summary>
    public bool IsRecordingEnabled { get; set; }
}

/// <summary>
/// Request model for joining a meeting
/// </summary>
public class JoinMeetingRequest
{
    /// <summary>
    /// Meeting code or ID
    /// </summary>
    [Required(ErrorMessage = "Meeting code is required")]
    public string MeetingCode { get; set; } = string.Empty;

    /// <summary>
    /// Meeting password (if required)
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Display name for the participant
    /// </summary>
    [Required(ErrorMessage = "Display name is required")]
    [StringLength(100, MinimumLength = 2, ErrorMessage = "Display name must be between 2 and 100 characters")]
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool IsCameraEnabled { get; set; } = true;

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool IsMicrophoneEnabled { get; set; } = true;
}

/// <summary>
/// Response model for joining a meeting
/// </summary>
public class JoinMeetingResponse
{
    /// <summary>
    /// Meeting information
    /// </summary>
    public MeetingDto Meeting { get; set; } = new();

    /// <summary>
    /// Participant information
    /// </summary>
    public MeetingParticipantDto Participant { get; set; } = new();

    /// <summary>
    /// SignalR connection token
    /// </summary>
    public string? ConnectionToken { get; set; }
}

/// <summary>
/// Request model for starting a meeting
/// </summary>
public class StartMeetingRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }
}

/// <summary>
/// Request model for ending a meeting
/// </summary>
public class EndMeetingRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Reason for ending the meeting
    /// </summary>
    [StringLength(500, ErrorMessage = "Reason cannot exceed 500 characters")]
    public string? Reason { get; set; }
}

/// <summary>
/// Request model for getting meetings with filters
/// </summary>
public class GetMeetingsRequest : PagedRequest
{
    /// <summary>
    /// Filter by meeting status
    /// </summary>
    public MeetingStatus? Status { get; set; }

    /// <summary>
    /// Filter by meeting type
    /// </summary>
    public MeetingType? Type { get; set; }

    /// <summary>
    /// Filter by date range start
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Filter by date range end
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Include only meetings where user is host
    /// </summary>
    public bool? HostedByUser { get; set; }

    /// <summary>
    /// Include only meetings where user is participant
    /// </summary>
    public bool? ParticipatedByUser { get; set; }
}
