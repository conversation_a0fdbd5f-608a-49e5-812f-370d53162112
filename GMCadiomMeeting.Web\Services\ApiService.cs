using GMCadiomMeeting.Web.Models;
using Newtonsoft.Json;
using System.Text;

namespace GMCadiomMeeting.Web.Services;

public interface IApiService
{
    Task<LoginResponse?> LoginAsync(LoginViewModel model);
    Task<UserViewModel?> RegisterAsync(RegisterViewModel model);
    Task<UserViewModel?> GetUserAsync(int userId);
    Task<List<MeetingViewModel>> GetUserMeetingsAsync(int userId);
    Task<MeetingViewModel?> GetMeetingAsync(int meetingId);
    Task<MeetingViewModel?> CreateMeetingAsync(CreateMeetingViewModel model, int hostUserId);
    Task<MeetingParticipantViewModel?> JoinMeetingAsync(JoinMeetingViewModel model, int userId);
    Task<List<InvitationViewModel>> GetUserInvitationsAsync(int userId);
    Task<List<InvitationViewModel>> GetMeetingInvitationsAsync(int meetingId);
    Task<bool> SendInvitationsAsync(SendInvitationViewModel model, int sentByUserId);
    Task<bool> RespondToInvitationAsync(int invitationId, bool accept);
    Task<List<ChatMessageViewModel>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50);
    Task<ChatMessageViewModel?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null);
}

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    private readonly IConfiguration _configuration;

    public ApiService(HttpClient httpClient, ILogger<ApiService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
        
        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7000";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
    }

    public async Task<LoginResponse?> LoginAsync(LoginViewModel model)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { email = model.Email, password = model.Password });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/users/login", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<LoginResponse>(responseContent);
            }
            
            _logger.LogWarning("Login failed for user {Email}. Status: {StatusCode}", model.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Email}", model.Email);
            return null;
        }
    }

    public async Task<UserViewModel?> RegisterAsync(RegisterViewModel model)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                email = model.Email,
                password = model.Password,
                displayName = model.DisplayName,
                firstName = model.FirstName,
                lastName = model.LastName,
                timeZone = model.TimeZone
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/users/register", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserViewModel>(responseContent);
            }
            
            _logger.LogWarning("Registration failed for user {Email}. Status: {StatusCode}", model.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration for user {Email}", model.Email);
            return null;
        }
    }

    public async Task<UserViewModel?> GetUserAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/users/{userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserViewModel>(responseContent);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<List<MeetingViewModel>> GetUserMeetingsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/user/{userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<MeetingViewModel>>(responseContent) ?? new List<MeetingViewModel>();
            }
            
            return new List<MeetingViewModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meetings for user {UserId}", userId);
            return new List<MeetingViewModel>();
        }
    }

    public async Task<MeetingViewModel?> GetMeetingAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/{meetingId}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingViewModel>(responseContent);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meeting {MeetingId}", meetingId);
            return null;
        }
    }

    public async Task<MeetingViewModel?> CreateMeetingAsync(CreateMeetingViewModel model, int hostUserId)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                title = model.Title,
                description = model.Description,
                type = (int)model.Type,
                scheduledStartTime = model.ScheduledStartTime,
                scheduledEndTime = model.ScheduledEndTime,
                hostUserId = hostUserId,
                maxParticipants = model.MaxParticipants,
                password = model.Password,
                isRecordingEnabled = model.IsRecordingEnabled,
                isRecurring = model.IsRecurring,
                recurrencePattern = model.RecurrencePattern
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/meetings", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingViewModel>(responseContent);
            }
            
            _logger.LogWarning("Meeting creation failed. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating meeting");
            return null;
        }
    }

    public async Task<MeetingParticipantViewModel?> JoinMeetingAsync(JoinMeetingViewModel model, int userId)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                userId = userId,
                displayName = model.DisplayName,
                password = model.Password,
                isCameraEnabled = model.IsCameraEnabled,
                isMicrophoneEnabled = model.IsMicrophoneEnabled
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"/api/meetings/{model.MeetingCode}/join", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingParticipantViewModel>(responseContent);
            }
            
            _logger.LogWarning("Join meeting failed for code {MeetingCode}. Status: {StatusCode}", model.MeetingCode, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting {MeetingCode}", model.MeetingCode);
            return null;
        }
    }

    public async Task<List<InvitationViewModel>> GetUserInvitationsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/user/{userId}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationViewModel>>(responseContent) ?? new List<InvitationViewModel>();
            }
            
            return new List<InvitationViewModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for user {UserId}", userId);
            return new List<InvitationViewModel>();
        }
    }

    public async Task<List<InvitationViewModel>> GetMeetingInvitationsAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/meeting/{meetingId}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationViewModel>>(responseContent) ?? new List<InvitationViewModel>();
            }
            
            return new List<InvitationViewModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for meeting {MeetingId}", meetingId);
            return new List<InvitationViewModel>();
        }
    }

    public async Task<bool> SendInvitationsAsync(SendInvitationViewModel model, int sentByUserId)
    {
        try
        {
            var emails = model.EmailAddresses.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Select(email => email.Trim())
                .Where(email => !string.IsNullOrEmpty(email))
                .ToList();

            var invitees = emails.Select(email => new
            {
                email = email,
                role = (int)model.InvitedRole
            }).ToList();

            var json = JsonConvert.SerializeObject(new
            {
                meetingId = model.MeetingId,
                sentByUserId = sentByUserId,
                invitees = invitees,
                personalMessage = model.PersonalMessage,
                expiresAt = model.ExpiresAt,
                allowJoinBeforeHost = model.AllowJoinBeforeHost
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/invitations/send", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitations for meeting {MeetingId}", model.MeetingId);
            return false;
        }
    }

    public async Task<bool> RespondToInvitationAsync(int invitationId, bool accept)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { accept = accept });
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"/api/invitations/{invitationId}/respond", content);
            
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error responding to invitation {InvitationId}", invitationId);
            return false;
        }
    }

    public async Task<List<ChatMessageViewModel>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/chat/meeting/{meetingId}?userId={userId}&page={page}&pageSize={pageSize}");
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<ChatMessageViewModel>>(responseContent) ?? new List<ChatMessageViewModel>();
            }
            
            return new List<ChatMessageViewModel>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages for meeting {MeetingId}", meetingId);
            return new List<ChatMessageViewModel>();
        }
    }

    public async Task<ChatMessageViewModel?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                meetingId = meetingId,
                senderId = senderId,
                recipientId = recipientId,
                content = content,
                type = 0, // Text
                scope = recipientId.HasValue ? 1 : 0 // Private if recipient, otherwise Public
            });
            var contentObj = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("/api/chat/send", contentObj);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ChatMessageViewModel>(responseContent);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat message in meeting {MeetingId}", meetingId);
            return null;
        }
    }
}
