using GMCadiomMeeting.Data.Models;

namespace GMCadiomMeeting.Api.Services.Interfaces;

/// <summary>
/// Interface for meeting business logic operations
/// </summary>
public interface IMeetingService
{
    /// <summary>
    /// Create a new meeting with validation
    /// </summary>
    Task<Meeting> CreateMeetingAsync(CreateMeetingDto dto);

    /// <summary>
    /// Get meeting by ID with authorization check
    /// </summary>
    Task<Meeting?> GetMeetingAsync(int meetingId, int requestingUserId);

    /// <summary>
    /// Get meetings for a user (hosted or participating)
    /// </summary>
    Task<IEnumerable<Meeting>> GetUserMeetingsAsync(int userId, MeetingStatus? status = null);

    /// <summary>
    /// Update meeting details
    /// </summary>
    Task<Meeting> UpdateMeetingAsync(int meetingId, UpdateMeetingDto dto, int requestingUserId);

    /// <summary>
    /// Start a meeting
    /// </summary>
    Task<Meeting> StartMeetingAsync(int meetingId, int hostUserId);

    /// <summary>
    /// End a meeting
    /// </summary>
    Task<Meeting> EndMeetingAsync(int meetingId, int hostUserId);

    /// <summary>
    /// Join a meeting by code
    /// </summary>
    Task<MeetingParticipant> JoinMeetingAsync(string meetingCode, JoinMeetingDto dto);

    /// <summary>
    /// Leave a meeting
    /// </summary>
    Task LeaveMeetingAsync(int meetingId, int userId);

    /// <summary>
    /// Update participant permissions
    /// </summary>
    Task<MeetingParticipant> UpdateParticipantPermissionsAsync(int meetingId, int participantUserId, UpdateParticipantPermissionsDto dto, int requestingUserId);

    /// <summary>
    /// Remove participant from meeting
    /// </summary>
    Task RemoveParticipantAsync(int meetingId, int participantUserId, int requestingUserId);

    /// <summary>
    /// Get meeting participants
    /// </summary>
    Task<IEnumerable<MeetingParticipant>> GetMeetingParticipantsAsync(int meetingId, int requestingUserId);

    /// <summary>
    /// Generate unique meeting code
    /// </summary>
    Task<string> GenerateUniqueMeetingCodeAsync();

    /// <summary>
    /// Validate meeting access
    /// </summary>
    Task<bool> CanUserAccessMeetingAsync(int meetingId, int userId);
}

// DTOs for service layer
public class CreateMeetingDto
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public MeetingType Type { get; set; }
    public DateTime ScheduledStartTime { get; set; }
    public DateTime ScheduledEndTime { get; set; }
    public int HostUserId { get; set; }
    public int MaxParticipants { get; set; } = 100;
    public string? Password { get; set; }
    public bool IsRecordingEnabled { get; set; } = false;
    public bool IsRecurring { get; set; } = false;
    public string? RecurrencePattern { get; set; }
}

public class UpdateMeetingDto
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public DateTime? ScheduledStartTime { get; set; }
    public DateTime? ScheduledEndTime { get; set; }
    public int? MaxParticipants { get; set; }
    public string? Password { get; set; }
    public bool? IsRecordingEnabled { get; set; }
}

public class JoinMeetingDto
{
    public int UserId { get; set; }
    public string? DisplayName { get; set; }
    public string? Password { get; set; }
    public bool IsCameraEnabled { get; set; } = false;
    public bool IsMicrophoneEnabled { get; set; } = false;
    public string? IpAddress { get; set; }
    public string? UserAgent { get; set; }
}

public class UpdateParticipantPermissionsDto
{
    public ParticipantRole? Role { get; set; }
    public bool? CanShareScreen { get; set; }
    public bool? CanUseChat { get; set; }
    public bool? CanUnmute { get; set; }
    public bool? CanTurnOnCamera { get; set; }
}
