using System.ComponentModel.DataAnnotations;
using GMCadiomMeeting.Shared.Enums;

namespace GMCadiomMeeting.Shared.Models;

/// <summary>
/// Chat message information for API responses
/// </summary>
public class ChatMessageDto
{
    /// <summary>
    /// Message unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// Sender participant ID
    /// </summary>
    public int SenderParticipantId { get; set; }

    /// <summary>
    /// Sender name
    /// </summary>
    public string SenderName { get; set; } = string.Empty;

    /// <summary>
    /// Recipient participant ID (for private messages)
    /// </summary>
    public int? RecipientParticipantId { get; set; }

    /// <summary>
    /// Recipient name (for private messages)
    /// </summary>
    public string? RecipientName { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    public ChatMessageType Type { get; set; }

    /// <summary>
    /// Message scope (public, private, hosts)
    /// </summary>
    public ChatMessageScope Scope { get; set; }

    /// <summary>
    /// Date and time when message was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Indicates if message was edited
    /// </summary>
    public bool IsEdited { get; set; }

    /// <summary>
    /// Date and time when message was edited
    /// </summary>
    public DateTime? EditedAt { get; set; }

    /// <summary>
    /// Indicates if message was deleted
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// File attachment information (if applicable)
    /// </summary>
    public FileAttachmentDto? Attachment { get; set; }
}

/// <summary>
/// Request model for sending a chat message
/// </summary>
public class SendChatMessageRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Message content
    /// </summary>
    [Required(ErrorMessage = "Message content is required")]
    [StringLength(2000, ErrorMessage = "Message cannot exceed 2000 characters")]
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// Message type
    /// </summary>
    public ChatMessageType Type { get; set; } = ChatMessageType.Text;

    /// <summary>
    /// Message scope
    /// </summary>
    public ChatMessageScope Scope { get; set; } = ChatMessageScope.Public;

    /// <summary>
    /// Recipient participant ID (for private messages)
    /// </summary>
    public int? RecipientParticipantId { get; set; }
}

/// <summary>
/// Request model for editing a chat message
/// </summary>
public class EditChatMessageRequest
{
    /// <summary>
    /// Message ID
    /// </summary>
    [Required(ErrorMessage = "Message ID is required")]
    public int MessageId { get; set; }

    /// <summary>
    /// New message content
    /// </summary>
    [Required(ErrorMessage = "Message content is required")]
    [StringLength(2000, ErrorMessage = "Message cannot exceed 2000 characters")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// Request model for deleting a chat message
/// </summary>
public class DeleteChatMessageRequest
{
    /// <summary>
    /// Message ID
    /// </summary>
    [Required(ErrorMessage = "Message ID is required")]
    public int MessageId { get; set; }
}

/// <summary>
/// Request model for getting chat messages
/// </summary>
public class GetChatMessagesRequest : PagedRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Filter by message type
    /// </summary>
    public ChatMessageType? Type { get; set; }

    /// <summary>
    /// Filter by message scope
    /// </summary>
    public ChatMessageScope? Scope { get; set; }

    /// <summary>
    /// Filter messages sent after this date
    /// </summary>
    public DateTime? SentAfter { get; set; }

    /// <summary>
    /// Filter messages sent before this date
    /// </summary>
    public DateTime? SentBefore { get; set; }
}

/// <summary>
/// File attachment information
/// </summary>
public class FileAttachmentDto
{
    /// <summary>
    /// File unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Original file name
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// File MIME type
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// URL to download the file
    /// </summary>
    public string DownloadUrl { get; set; } = string.Empty;

    /// <summary>
    /// Date when file was uploaded
    /// </summary>
    public DateTime UploadedAt { get; set; }
}

/// <summary>
/// Screen sharing session information
/// </summary>
public class ScreenSharingDto
{
    /// <summary>
    /// Screen sharing session unique identifier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID who is sharing
    /// </summary>
    public int ParticipantId { get; set; }

    /// <summary>
    /// Name of participant sharing screen
    /// </summary>
    public string ParticipantName { get; set; } = string.Empty;

    /// <summary>
    /// Type of screen sharing
    /// </summary>
    public ScreenSharingType Type { get; set; }

    /// <summary>
    /// Title or description of what's being shared
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Indicates if screen sharing is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Quality level of the screen share
    /// </summary>
    public QualityLevel Quality { get; set; }

    /// <summary>
    /// Date and time when screen sharing started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// Date and time when screen sharing ended
    /// </summary>
    public DateTime? EndedAt { get; set; }

    /// <summary>
    /// Duration of screen sharing in minutes
    /// </summary>
    public int? DurationMinutes { get; set; }
}

/// <summary>
/// Request model for starting screen sharing
/// </summary>
public class StartScreenSharingRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Type of screen sharing
    /// </summary>
    public ScreenSharingType Type { get; set; } = ScreenSharingType.FullScreen;

    /// <summary>
    /// Title or description of what's being shared
    /// </summary>
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string? Title { get; set; }

    /// <summary>
    /// Quality level for the screen share
    /// </summary>
    public QualityLevel Quality { get; set; } = QualityLevel.Medium;
}

/// <summary>
/// Request model for stopping screen sharing
/// </summary>
public class StopScreenSharingRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Screen sharing session ID
    /// </summary>
    [Required(ErrorMessage = "Screen sharing session ID is required")]
    public int ScreenSharingId { get; set; }
}

/// <summary>
/// Request model for updating media state
/// </summary>
public class UpdateMediaStateRequest
{
    /// <summary>
    /// Meeting ID
    /// </summary>
    [Required(ErrorMessage = "Meeting ID is required")]
    public int MeetingId { get; set; }

    /// <summary>
    /// Indicates if camera is enabled
    /// </summary>
    public bool IsCameraEnabled { get; set; }

    /// <summary>
    /// Indicates if microphone is enabled
    /// </summary>
    public bool IsMicrophoneEnabled { get; set; }

    /// <summary>
    /// Video quality level
    /// </summary>
    public QualityLevel? VideoQuality { get; set; }

    /// <summary>
    /// Audio quality level
    /// </summary>
    public QualityLevel? AudioQuality { get; set; }
}

/// <summary>
/// Real-time event notification
/// </summary>
public class RealTimeEventDto
{
    /// <summary>
    /// Event type
    /// </summary>
    public string EventType { get; set; } = string.Empty;

    /// <summary>
    /// Meeting ID
    /// </summary>
    public int MeetingId { get; set; }

    /// <summary>
    /// Participant ID who triggered the event
    /// </summary>
    public int? ParticipantId { get; set; }

    /// <summary>
    /// Event data (JSON serialized)
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// Event timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
