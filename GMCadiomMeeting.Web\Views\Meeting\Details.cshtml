@model GMCadiomMeeting.Web.Models.MeetingViewModel
@{
    ViewData["Title"] = $"Meeting: {Model.Title}";
    var isHost = ViewBag.User != null && Model.HostUserId == ((GMCadiomMeeting.Web.Models.UserViewModel)ViewBag.User).Id;
    var canJoin = Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.Scheduled || Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.InProgress;
}

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <!-- Meeting Header -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1">@Model.Title</h4>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-light text-dark me-2">@Model.Type</span>
                                <span class="badge bg-@(Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.Scheduled ? "warning" : 
                                                      Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.InProgress ? "success" : 
                                                      Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.Ended ? "secondary" : "danger")">
                                    @Model.Status
                                </span>
                            </div>
                        </div>
                        @if (canJoin)
                        {
                            <a asp-action="Room" asp-route-id="@Model.Id" class="btn btn-light btn-lg">
                                <i class="fas fa-video me-2"></i>Join Meeting
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div class="mb-4">
                            <h6 class="text-muted mb-2">Description</h6>
                            <p class="mb-0">@Model.Description</p>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-calendar me-1"></i>Scheduled Time
                            </h6>
                            <div class="fw-semibold">@Model.ScheduledStartTime.ToString("MMM dd, yyyy")</div>
                            <div class="text-muted">@Model.ScheduledStartTime.ToString("h:mm tt") - @Model.ScheduledEndTime.ToString("h:mm tt")</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-user me-1"></i>Host
                            </h6>
                            <div class="fw-semibold">@(Model.HostUser?.DisplayName ?? "Unknown")</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-key me-1"></i>Meeting Code
                            </h6>
                            <div class="d-flex align-items-center">
                                <code class="bg-light p-2 rounded me-2">@Model.MeetingCode</code>
                                <button class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('@Model.MeetingCode', 'Meeting code copied!')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-users me-1"></i>Participants
                            </h6>
                            <div class="fw-semibold">@Model.Participants.Count / @Model.MaxParticipants</div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.InvitationLink))
                    {
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">
                                <i class="fas fa-link me-1"></i>Invitation Link
                            </h6>
                            <div class="input-group">
                                <input type="text" class="form-control" value="@Model.InvitationLink" readonly>
                                <button class="btn btn-outline-primary" onclick="copyToClipboard('@Model.InvitationLink', 'Invitation link copied!')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    }

                    @if (Model.IsRecordingEnabled)
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-record-vinyl me-2"></i>
                            This meeting will be recorded
                        </div>
                    }
                </div>
            </div>

            <!-- Participants List -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>Participants (@Model.Participants.Count)
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Participants.Where(x=> x != null).Any())
                    {
                        <div class="row">
                            @foreach (var participant in Model.Participants)
                            {
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-semibold">@(participant.DisplayNameInMeeting ?? participant.User?.DisplayName)</div>
                                            <div class="small">
                                                <span class="badge bg-@(participant.Role == GMCadiomMeeting.Web.Models.ParticipantRole.Host ? "primary" : 
                                                                      participant.Role == GMCadiomMeeting.Web.Models.ParticipantRole.CoHost ? "info" : 
                                                                      participant.Role == GMCadiomMeeting.Web.Models.ParticipantRole.Presenter ? "warning" : "secondary") me-1">
                                                    @participant.Role
                                                </span>
                                                <span class="badge bg-@(participant.Status == GMCadiomMeeting.Web.Models.ConnectionStatus.Connected ? "success" : "secondary")">
                                                    @participant.Status
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-3">
                            <i class="fas fa-user-plus fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No participants yet</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom py-3">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    @if (canJoin)
                    {
                        <a asp-action="Room" asp-route-id="@Model.Id" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-video me-2"></i>Join Meeting
                        </a>
                    }

                    @if (isHost)
                    {
                        <a asp-action="Invitations" asp-route-id="@Model.Id" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-envelope me-2"></i>Manage Invitations
                        </a>
                        <a asp-action="SendInvitation" asp-route-id="@Model.Id" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-paper-plane me-2"></i>Send Invitations
                        </a>
                        
                        @if (Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.Scheduled)
                        {
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="startMeeting(@Model.Id)">
                                <i class="fas fa-play me-2"></i>Start Meeting
                            </button>
                        }
                        
                        @if (Model.Status == GMCadiomMeeting.Web.Models.MeetingStatus.InProgress)
                        {
                            <button class="btn btn-outline-danger w-100 mb-2" onclick="endMeeting(@Model.Id)">
                                <i class="fas fa-stop me-2"></i>End Meeting
                            </button>
                        }
                    }

                    <a asp-controller="Home" asp-action="Dashboard" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Meeting Info -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom py-3">
                    <h6 class="mb-0">Meeting Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Created</small>
                        <div>@Model.CreatedAt.ToString("MMM dd, yyyy h:mm tt")</div>
                    </div>
                    
                    @if (Model.ActualStartTime.HasValue)
                    {
                        <div class="mb-3">
                            <small class="text-muted">Started</small>
                            <div>@Model.ActualStartTime.Value.ToString("MMM dd, yyyy h:mm tt")</div>
                        </div>
                    }
                    
                    @if (Model.ActualEndTime.HasValue)
                    {
                        <div class="mb-3">
                            <small class="text-muted">Ended</small>
                            <div>@Model.ActualEndTime.Value.ToString("MMM dd, yyyy h:mm tt")</div>
                        </div>
                    }

                    <div class="mb-3">
                        <small class="text-muted">Max Participants</small>
                        <div>@Model.MaxParticipants</div>
                    </div>

                    @if (Model.IsRecurring)
                    {
                        <div class="mb-3">
                            <small class="text-muted">Recurrence</small>
                            <div>@(Model.RecurrencePattern ?? "Custom")</div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.RecordingUrl))
                    {
                        <div class="mb-3">
                            <small class="text-muted">Recording</small>
                            <div>
                                <a href="@Model.RecordingUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-play me-1"></i>View Recording
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function startMeeting(meetingId) {
            if (confirm('Are you sure you want to start this meeting?')) {
                // TODO: Implement start meeting API call
                showToast('Meeting started!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }

        function endMeeting(meetingId) {
            if (confirm('Are you sure you want to end this meeting? This action cannot be undone.')) {
                // TODO: Implement end meeting API call
                showToast('Meeting ended!', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }
    </script>
}
