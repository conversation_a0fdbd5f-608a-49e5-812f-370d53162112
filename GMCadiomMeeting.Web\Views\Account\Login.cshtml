@model GMCadiomMeeting.Web.Models.LoginViewModel
@{
    ViewData["Title"] = "Login";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-video fa-3x text-primary mb-3"></i>
                        <h2 class="h4 mb-2">Welcome Back</h2>
                        <p class="text-muted">Sign in to your GMCadiom Meeting account</p>
                    </div>

                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input asp-for="Password" class="form-control" placeholder="Enter your password" />
                            </div>
                            <span asp-validation-for="Password" class="text-danger small"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Remember me
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">
                                Don't have an account? 
                                <a asp-action="Register" class="text-decoration-none">Create one here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <p class="text-muted">
                    <a asp-controller="Meeting" asp-action="Join" class="text-decoration-none">
                        <i class="fas fa-users me-1"></i>Join a meeting as guest
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
