namespace GMCadiomMeeting.Shared.Constants;

/// <summary>
/// Application-wide constants
/// </summary>
public static class ApplicationConstants
{
    /// <summary>
    /// API version information
    /// </summary>
    public static class ApiVersion
    {
        public const string V1 = "v1";
        public const string Current = V1;
    }

    /// <summary>
    /// Authentication and authorization constants
    /// </summary>
    public static class Auth
    {
        public const string JwtScheme = "Bearer";
        public const string SessionScheme = "Session";
        public const string DefaultPolicy = "DefaultPolicy";
        public const string AdminPolicy = "AdminPolicy";
        public const string HostPolicy = "HostPolicy";
        
        /// <summary>
        /// JWT claim types
        /// </summary>
        public static class Claims
        {
            public const string UserId = "user_id";
            public const string Email = "email";
            public const string DisplayName = "display_name";
            public const string Role = "role";
            public const string IsEmailVerified = "email_verified";
            public const string TimeZone = "timezone";
        }

        /// <summary>
        /// User roles
        /// </summary>
        public static class Roles
        {
            public const string Admin = "Admin";
            public const string User = "User";
            public const string Guest = "Guest";
        }
    }

    /// <summary>
    /// Meeting-related constants
    /// </summary>
    public static class Meeting
    {
        public const int MinTitleLength = 3;
        public const int MaxTitleLength = 200;
        public const int MaxDescriptionLength = 1000;
        public const int MinParticipants = 2;
        public const int MaxParticipants = 1000;
        public const int DefaultMaxParticipants = 100;
        public const int MeetingCodeLength = 9; // XXX-XXX-XXX format
        public const int MaxPasswordLength = 50;
        public const int DefaultDurationMinutes = 60;
        public const int MaxDurationHours = 24;
        
        /// <summary>
        /// Meeting code generation patterns
        /// </summary>
        public static class CodePatterns
        {
            public const string Standard = @"^\d{3}-\d{3}-\d{3}$";
            public const string Numeric = @"^\d{9}$";
        }
    }

    /// <summary>
    /// Chat and messaging constants
    /// </summary>
    public static class Chat
    {
        public const int MaxMessageLength = 2000;
        public const int MaxPersonalMessageLength = 1000;
        public const int MaxFileNameLength = 255;
        public const long MaxFileSizeBytes = 10 * 1024 * 1024; // 10 MB
        public const int MessageHistoryDays = 30;
        
        /// <summary>
        /// Allowed file types for chat attachments
        /// </summary>
        public static readonly string[] AllowedFileExtensions = 
        {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
            ".txt", ".rtf", ".csv", ".zip", ".rar", ".7z",
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
            ".mp3", ".wav", ".mp4", ".avi", ".mov", ".wmv"
        };
        
        /// <summary>
        /// MIME types for allowed file extensions
        /// </summary>
        public static readonly Dictionary<string, string> MimeTypes = new()
        {
            { ".pdf", "application/pdf" },
            { ".doc", "application/msword" },
            { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            { ".xls", "application/vnd.ms-excel" },
            { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            { ".ppt", "application/vnd.ms-powerpoint" },
            { ".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation" },
            { ".txt", "text/plain" },
            { ".csv", "text/csv" },
            { ".zip", "application/zip" },
            { ".jpg", "image/jpeg" },
            { ".jpeg", "image/jpeg" },
            { ".png", "image/png" },
            { ".gif", "image/gif" },
            { ".mp3", "audio/mpeg" },
            { ".mp4", "video/mp4" }
        };
    }

    /// <summary>
    /// Invitation-related constants
    /// </summary>
    public static class Invitation
    {
        public const int MaxEmailsPerInvitation = 50;
        public const int DefaultExpirationHours = 24;
        public const int MaxExpirationDays = 30;
        public const int TokenLength = 32;
        public const int MaxPersonalMessageLength = 1000;
        public const int ReminderMinutesBeforeMeeting = 60;
    }

    /// <summary>
    /// User profile constants
    /// </summary>
    public static class User
    {
        public const int MinDisplayNameLength = 2;
        public const int MaxDisplayNameLength = 100;
        public const int MaxFirstNameLength = 50;
        public const int MaxLastNameLength = 50;
        public const int MaxEmailLength = 255;
        public const int MinPasswordLength = 6;
        public const int MaxPasswordLength = 100;
        public const int MaxProfilePictureUrlLength = 500;
        public const int MaxTimeZoneLength = 50;
        public const int EmailVerificationTokenLength = 32;
        public const int PasswordResetTokenLength = 32;
        public const int EmailVerificationExpirationHours = 24;
        public const int PasswordResetExpirationHours = 2;
    }

    /// <summary>
    /// Pagination constants
    /// </summary>
    public static class Pagination
    {
        public const int DefaultPageSize = 10;
        public const int MaxPageSize = 100;
        public const int MinPageSize = 1;
        public const int DefaultPageNumber = 1;
    }

    /// <summary>
    /// Cache keys and expiration times
    /// </summary>
    public static class Cache
    {
        public const int DefaultExpirationMinutes = 30;
        public const int UserCacheExpirationMinutes = 60;
        public const int MeetingCacheExpirationMinutes = 15;
        
        /// <summary>
        /// Cache key patterns
        /// </summary>
        public static class Keys
        {
            public const string UserById = "user:id:{0}";
            public const string UserByEmail = "user:email:{0}";
            public const string MeetingById = "meeting:id:{0}";
            public const string MeetingByCode = "meeting:code:{0}";
            public const string MeetingParticipants = "meeting:participants:{0}";
            public const string UserMeetings = "user:meetings:{0}";
            public const string UserInvitations = "user:invitations:{0}";
        }
    }

    /// <summary>
    /// SignalR hub and event constants
    /// </summary>
    public static class SignalR
    {
        public const string MeetingHubPath = "/meetingHub";
        
        /// <summary>
        /// SignalR event names
        /// </summary>
        public static class Events
        {
            public const string ParticipantJoined = "ParticipantJoined";
            public const string ParticipantLeft = "ParticipantLeft";
            public const string ParticipantMediaStateChanged = "ParticipantMediaStateChanged";
            public const string ChatMessage = "ChatMessage";
            public const string ScreenSharingStarted = "ScreenSharingStarted";
            public const string ScreenSharingStopped = "ScreenSharingStopped";
            public const string MeetingStarted = "MeetingStarted";
            public const string MeetingEnded = "MeetingEnded";
            public const string ParticipantPromoted = "ParticipantPromoted";
            public const string ParticipantMuted = "ParticipantMuted";
            public const string ParticipantRemoved = "ParticipantRemoved";
        }
        
        /// <summary>
        /// SignalR group names
        /// </summary>
        public static class Groups
        {
            public const string MeetingGroup = "Meeting_{0}";
            public const string UserGroup = "User_{0}";
            public const string HostsGroup = "Meeting_{0}_Hosts";
        }
    }

    /// <summary>
    /// Email template constants
    /// </summary>
    public static class EmailTemplates
    {
        public const string WelcomeEmail = "WelcomeEmail";
        public const string EmailVerification = "EmailVerification";
        public const string PasswordReset = "PasswordReset";
        public const string MeetingInvitation = "MeetingInvitation";
        public const string MeetingReminder = "MeetingReminder";
        public const string MeetingCancellation = "MeetingCancellation";
        public const string MeetingUpdate = "MeetingUpdate";
    }

    /// <summary>
    /// File storage constants
    /// </summary>
    public static class Storage
    {
        public const string ProfilePicturesContainer = "profile-pictures";
        public const string ChatAttachmentsContainer = "chat-attachments";
        public const string MeetingRecordingsContainer = "meeting-recordings";
        public const string TempFilesContainer = "temp-files";
        
        /// <summary>
        /// File retention periods
        /// </summary>
        public static class Retention
        {
            public const int TempFilesDays = 1;
            public const int ChatAttachmentsDays = 90;
            public const int MeetingRecordingsDays = 365;
        }
    }

    /// <summary>
    /// Rate limiting constants
    /// </summary>
    public static class RateLimit
    {
        public const int LoginAttemptsPerMinute = 5;
        public const int RegistrationAttemptsPerHour = 3;
        public const int EmailVerificationAttemptsPerHour = 3;
        public const int PasswordResetAttemptsPerHour = 3;
        public const int ChatMessagesPerMinute = 30;
        public const int InvitationsPerHour = 20;
        public const int MeetingCreationsPerHour = 10;
    }

    /// <summary>
    /// Logging event IDs
    /// </summary>
    public static class LoggingEvents
    {
        public const int UserRegistered = 1001;
        public const int UserLoggedIn = 1002;
        public const int UserLoggedOut = 1003;
        public const int MeetingCreated = 2001;
        public const int MeetingStarted = 2002;
        public const int MeetingEnded = 2003;
        public const int ParticipantJoined = 3001;
        public const int ParticipantLeft = 3002;
        public const int InvitationSent = 4001;
        public const int InvitationAccepted = 4002;
        public const int InvitationDeclined = 4003;
        public const int ChatMessageSent = 5001;
        public const int ScreenSharingStarted = 6001;
        public const int ScreenSharingStopped = 6002;
    }
}
