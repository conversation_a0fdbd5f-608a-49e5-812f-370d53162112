using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using GMCadiomMeeting.Data.Context;
using GMCadiomMeeting.Data.Models;

namespace GMCadiomMeeting.Api.Controllers;

/// <summary>
/// Controller for managing in-meeting chat functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ChatController : ControllerBase
{
    private readonly GMCadiomMeetingDbContext _context;
    private readonly ILogger<ChatController> _logger;

    public ChatController(GMCadiomMeetingDbContext context, ILogger<ChatController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Send a chat message in a meeting
    /// </summary>
    [HttpPost("send")]
    public async Task<ActionResult<ChatMessageResponse>> SendMessage(SendMessageRequest request)
    {
        try
        {
            // Verify user is a participant in the meeting
            var participant = await _context.MeetingParticipants
                .Include(p => p.Meeting)
                .FirstOrDefaultAsync(p => p.MeetingId == request.MeetingId && 
                                         p.UserId == request.SenderId &&
                                         p.CanUseChat);

            if (participant == null)
            {
                return Forbid("User is not authorized to send messages in this meeting");
            }

            if (participant.Meeting?.Status != MeetingStatus.InProgress)
            {
                return BadRequest("Cannot send messages in a meeting that is not in progress");
            }

            // Validate recipient for private messages
            if (request.Scope == MessageScope.Private && request.RecipientId.HasValue)
            {
                var recipientParticipant = await _context.MeetingParticipants
                    .FirstOrDefaultAsync(p => p.MeetingId == request.MeetingId && 
                                             p.UserId == request.RecipientId.Value);

                if (recipientParticipant == null)
                {
                    return BadRequest("Recipient is not a participant in this meeting");
                }
            }

            var message = new ChatMessage
            {
                MeetingId = request.MeetingId,
                SenderId = request.SenderId,
                RecipientId = request.RecipientId,
                Content = request.Content,
                Type = request.Type,
                Scope = request.Scope,
                ReplyToMessageId = request.ReplyToMessageId,
                FileUrl = request.FileUrl,
                FileName = request.FileName,
                FileSize = request.FileSize,
                FileMimeType = request.FileMimeType,
                SentAt = DateTime.UtcNow
            };

            _context.ChatMessages.Add(message);
            await _context.SaveChangesAsync();

            // Load complete message data for response
            var savedMessage = await _context.ChatMessages
                .Include(m => m.Sender)
                .Include(m => m.Recipient)
                .Include(m => m.ReplyToMessage)
                    .ThenInclude(rm => rm.Sender)
                .FirstOrDefaultAsync(m => m.Id == message.Id);

            var response = MapToResponse(savedMessage!);

            // TODO: Send real-time notification via SignalR
            _logger.LogInformation("Message sent in meeting {MeetingId} by user {UserId}", 
                request.MeetingId, request.SenderId);

            return CreatedAtAction(nameof(GetMessage), new { id = message.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending message in meeting {MeetingId}", request.MeetingId);
            return StatusCode(500, "An error occurred while sending the message");
        }
    }

    /// <summary>
    /// Get chat messages for a meeting
    /// </summary>
    [HttpGet("meeting/{meetingId}")]
    public async Task<ActionResult<IEnumerable<ChatMessageResponse>>> GetMeetingMessages(
        int meetingId, 
        [FromQuery] int userId,
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 50,
        [FromQuery] MessageScope? scope = null)
    {
        try
        {
            // Verify user is a participant
            var participant = await _context.MeetingParticipants
                .FirstOrDefaultAsync(p => p.MeetingId == meetingId && p.UserId == userId);

            if (participant == null)
            {
                return Forbid("User is not a participant in this meeting");
            }

            var query = _context.ChatMessages
                .Where(m => m.MeetingId == meetingId && !m.IsDeleted)
                .Include(m => m.Sender)
                .Include(m => m.Recipient)
                .Include(m => m.ReplyToMessage)
                    .ThenInclude(rm => rm.Sender)
                .AsQueryable();

            // Filter messages based on scope and user permissions
            query = query.Where(m => 
                m.Scope == MessageScope.Public ||
                (m.Scope == MessageScope.Private && (m.SenderId == userId || m.RecipientId == userId)) ||
                (m.Scope == MessageScope.Hosts && (participant.Role == ParticipantRole.Host || participant.Role == ParticipantRole.CoHost)) ||
                (m.Scope == MessageScope.Presenters && (participant.Role == ParticipantRole.Host || participant.Role == ParticipantRole.CoHost || participant.Role == ParticipantRole.Presenter))
            );

            if (scope.HasValue)
            {
                query = query.Where(m => m.Scope == scope.Value);
            }

            var messages = await query
                .OrderByDescending(m => m.SentAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var responses = messages.Select(MapToResponse).Reverse().ToList();

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving messages for meeting {MeetingId}", meetingId);
            return StatusCode(500, "An error occurred while retrieving messages");
        }
    }

    /// <summary>
    /// Get a specific message by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ChatMessageResponse>> GetMessage(int id)
    {
        try
        {
            var message = await _context.ChatMessages
                .Include(m => m.Sender)
                .Include(m => m.Recipient)
                .Include(m => m.ReplyToMessage)
                    .ThenInclude(rm => rm.Sender)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

            if (message == null)
            {
                return NotFound($"Message with ID {id} not found");
            }

            var response = MapToResponse(message);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving message {MessageId}", id);
            return StatusCode(500, "An error occurred while retrieving the message");
        }
    }

    /// <summary>
    /// Edit a chat message
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ChatMessageResponse>> EditMessage(int id, EditMessageRequest request)
    {
        try
        {
            var message = await _context.ChatMessages
                .Include(m => m.Sender)
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

            if (message == null)
            {
                return NotFound($"Message with ID {id} not found");
            }

            if (message.SenderId != request.UserId)
            {
                return Forbid("You can only edit your own messages");
            }

            if (message.SentAt < DateTime.UtcNow.AddMinutes(-15))
            {
                return BadRequest("Messages can only be edited within 15 minutes of sending");
            }

            // Store original content for audit
            if (!message.IsEdited)
            {
                message.OriginalContent = message.Content;
            }

            message.Content = request.NewContent;
            message.IsEdited = true;
            message.EditedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var response = MapToResponse(message);

            // TODO: Send real-time update via SignalR
            _logger.LogInformation("Message {MessageId} edited by user {UserId}", id, request.UserId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error editing message {MessageId}", id);
            return StatusCode(500, "An error occurred while editing the message");
        }
    }

    /// <summary>
    /// Delete a chat message
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteMessage(int id, [FromQuery] int userId)
    {
        try
        {
            var message = await _context.ChatMessages
                .FirstOrDefaultAsync(m => m.Id == id && !m.IsDeleted);

            if (message == null)
            {
                return NotFound($"Message with ID {id} not found");
            }

            // Check if user can delete the message (sender or meeting host)
            var canDelete = message.SenderId == userId;
            
            if (!canDelete)
            {
                var participant = await _context.MeetingParticipants
                    .FirstOrDefaultAsync(p => p.MeetingId == message.MeetingId && 
                                             p.UserId == userId &&
                                             (p.Role == ParticipantRole.Host || p.Role == ParticipantRole.CoHost));
                canDelete = participant != null;
            }

            if (!canDelete)
            {
                return Forbid("You can only delete your own messages or you must be a host/co-host");
            }

            // Soft delete
            message.IsDeleted = true;
            message.DeletedAt = DateTime.UtcNow;
            message.Content = "[Message deleted]";

            await _context.SaveChangesAsync();

            // TODO: Send real-time update via SignalR
            _logger.LogInformation("Message {MessageId} deleted by user {UserId}", id, userId);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting message {MessageId}", id);
            return StatusCode(500, "An error occurred while deleting the message");
        }
    }

    /// <summary>
    /// Get private conversation between two users in a meeting
    /// </summary>
    [HttpGet("meeting/{meetingId}/conversation")]
    public async Task<ActionResult<IEnumerable<ChatMessageResponse>>> GetPrivateConversation(
        int meetingId, 
        [FromQuery] int userId1, 
        [FromQuery] int userId2,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 50)
    {
        try
        {
            var messages = await _context.ChatMessages
                .Where(m => m.MeetingId == meetingId && 
                           m.Scope == MessageScope.Private &&
                           !m.IsDeleted &&
                           ((m.SenderId == userId1 && m.RecipientId == userId2) ||
                            (m.SenderId == userId2 && m.RecipientId == userId1)))
                .Include(m => m.Sender)
                .Include(m => m.Recipient)
                .OrderByDescending(m => m.SentAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var responses = messages.Select(MapToResponse).Reverse().ToList();

            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving private conversation in meeting {MeetingId}", meetingId);
            return StatusCode(500, "An error occurred while retrieving the conversation");
        }
    }

    private ChatMessageResponse MapToResponse(ChatMessage message)
    {
        return new ChatMessageResponse
        {
            Id = message.Id,
            MeetingId = message.MeetingId,
            SenderId = message.SenderId,
            SenderName = message.Sender?.DisplayName ?? "Unknown",
            RecipientId = message.RecipientId,
            RecipientName = message.Recipient?.DisplayName,
            Content = message.Content,
            Type = message.Type,
            Scope = message.Scope,
            IsEdited = message.IsEdited,
            IsDeleted = message.IsDeleted,
            FileUrl = message.FileUrl,
            FileName = message.FileName,
            FileSize = message.FileSize,
            FileMimeType = message.FileMimeType,
            SentAt = message.SentAt,
            EditedAt = message.EditedAt,
            ReplyToMessageId = message.ReplyToMessageId,
            ReplyToMessage = message.ReplyToMessage != null ? new ReplyMessageInfo
            {
                Id = message.ReplyToMessage.Id,
                Content = message.ReplyToMessage.Content,
                SenderName = message.ReplyToMessage.Sender?.DisplayName ?? "Unknown",
                SentAt = message.ReplyToMessage.SentAt
            } : null
        };
    }
}

// Request/Response DTOs
public class SendMessageRequest
{
    public int MeetingId { get; set; }
    public int SenderId { get; set; }
    public int? RecipientId { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType Type { get; set; } = MessageType.Text;
    public MessageScope Scope { get; set; } = MessageScope.Public;
    public int? ReplyToMessageId { get; set; }
    public string? FileUrl { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileMimeType { get; set; }
}

public class EditMessageRequest
{
    public int UserId { get; set; }
    public string NewContent { get; set; } = string.Empty;
}

public class ChatMessageResponse
{
    public int Id { get; set; }
    public int MeetingId { get; set; }
    public int SenderId { get; set; }
    public string SenderName { get; set; } = string.Empty;
    public int? RecipientId { get; set; }
    public string? RecipientName { get; set; }
    public string Content { get; set; } = string.Empty;
    public MessageType Type { get; set; }
    public MessageScope Scope { get; set; }
    public bool IsEdited { get; set; }
    public bool IsDeleted { get; set; }
    public string? FileUrl { get; set; }
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileMimeType { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? EditedAt { get; set; }
    public int? ReplyToMessageId { get; set; }
    public ReplyMessageInfo? ReplyToMessage { get; set; }
}

public class ReplyMessageInfo
{
    public int Id { get; set; }
    public string Content { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
}
