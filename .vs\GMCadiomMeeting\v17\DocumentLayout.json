{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}|GMCadiomMeeting.Api\\GMCadiomMeeting.Api.csproj|e:\\projects\\gmcadiom\\gmcadiommeeting\\gmcadiommeeting.api\\controllers\\meetingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6DDEA4A4-22E3-311C-AB0A-5EF7AA877173}|GMCadiomMeeting.Api\\GMCadiomMeeting.Api.csproj|solutionrelative:gmcadiommeeting.api\\controllers\\meetingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{506CBAC5-BEBE-8B78-82B9-1F00C967E497}|GMCadiomMeeting.Data\\GMCadiomMeeting.Data.csproj|e:\\projects\\gmcadiom\\gmcadiommeeting\\gmcadiommeeting.data\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{506CBAC5-BEBE-8B78-82B9-1F00C967E497}|GMCadiomMeeting.Data\\GMCadiomMeeting.Data.csproj|solutionrelative:gmcadiommeeting.data\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{506CBAC5-BEBE-8B78-82B9-1F00C967E497}|GMCadiomMeeting.Data\\GMCadiomMeeting.Data.csproj|e:\\projects\\gmcadiom\\gmcadiommeeting\\gmcadiommeeting.data\\models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{506CBAC5-BEBE-8B78-82B9-1F00C967E497}|GMCadiomMeeting.Data\\GMCadiomMeeting.Data.csproj|solutionrelative:gmcadiommeeting.data\\models\\chatmessage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MeetingsController.cs", "DocumentMoniker": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Api\\Controllers\\MeetingsController.cs", "RelativeDocumentMoniker": "GMCadiomMeeting.Api\\Controllers\\MeetingsController.cs", "ToolTip": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Api\\Controllers\\MeetingsController.cs", "RelativeToolTip": "GMCadiomMeeting.Api\\Controllers\\MeetingsController.cs", "ViewState": "AgIAAI8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T20:19:02.8Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UserConfiguration.cs", "DocumentMoniker": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Data\\Configurations\\UserConfiguration.cs", "RelativeDocumentMoniker": "GMCadiomMeeting.Data\\Configurations\\UserConfiguration.cs", "ToolTip": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Data\\Configurations\\UserConfiguration.cs", "RelativeToolTip": "GMCadiomMeeting.Data\\Configurations\\UserConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T20:18:46.983Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ChatMessage.cs", "DocumentMoniker": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Data\\Models\\ChatMessage.cs", "RelativeDocumentMoniker": "GMCadiomMeeting.Data\\Models\\ChatMessage.cs", "ToolTip": "E:\\Projects\\GMCadiom\\GMCadiomMeeting\\GMCadiomMeeting.Data\\Models\\ChatMessage.cs", "RelativeToolTip": "GMCadiomMeeting.Data\\Models\\ChatMessage.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T20:18:24.769Z", "EditorCaption": ""}]}]}]}